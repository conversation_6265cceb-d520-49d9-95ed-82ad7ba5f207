<?php

namespace Settings\Controller;

use MT\Logger\LoggerZend,
    MT\Sencha\Response,
    Core\Model\Session\Session,
    Database\Setting\Institute,
    Database\Setting\InstituteQuery,
    BasePeer,
    Criteria,
    Zend\Mvc\Controller\AbstractRestfulController,
    Zend\View\Model\JsonModel;

class InstituteController extends AbstractRestfulController {

    protected $logger;
    protected $session_auth;
    protected $fromLocal;

    public function __construct() {
        $this->logger = new LoggerZend;
        $this->session_auth = Session::readSessionAuth();
        $this->fromLocal = $_SERVER['REMOTE_ADDR'] == '127.0.0.1' || $_SERVER['REMOTE_ADDR'] == '::1' ? true : false;
    }

    /**
     *
     * @return JsonModel
     */
    public function getList() {
        $response = new Response;

        if ($this->session_auth['uid'] > 0) {
            $response->setSuccess(true);

            $filter = $this->params()->fromQuery();

            $iq = InstituteQuery::create()->filterByActive(false);

            $cnt = $iq->count();

            $institutes = $iq->orderByName()
                    ->orderByMeccanographicCode()
                    ->limit($filter['limit'])
                    ->offset($filter['start'])
                    ->find()
                    ->toArray(null, false, BasePeer::TYPE_FIELDNAME);

            $response->total = $cnt;
            $response->results = $institutes;
        }

        return new JsonModel((array) $response);
    }

    /**
     *
     * @param int $id
     * @return JsonModel
     */
    public function get($id) {
        $response = new Response;

        if ($this->session_auth['uid'] > 0) {
            $institute = InstituteQuery::create()->findOneById($id);

            if ($institute !== null) {
                $response->setSuccess(true);
                $this->results = $institute->toArray(BasePeer::TYPE_FIELDNAME);
            } else {
                $response->message = 'Institute not found';
            }
        }

        return new JsonModel((array) $response);
    }

    /**
     *
     * @param array $data
     * @return JsonModel
     */
    public function create($data) {
        $response = new Response;
        exit('ciao');

        if ($this->session_auth['uid'] > 0 || $this->fromLocal) {
            $institute = new Institute;

            if(empty($data['name'])) {
                $response->message = 'Name is required';
                return new JsonModel((array) $response);
            }

            if(empty($data['mechan_code'])) {
                $response->message = 'Mechan code is required';
                return new JsonModel((array) $response);
            }

            $institute->setName($data['name']);
            $institute->setMeccanographicCode($data['mechan_code']);

            if ($institute->save() > 0) {
                // Logging
                $this->logger->log(array(
                    'Type'    => 'INFO',
                    'Scope'   => __FILE__,
                    'Event'   => basename(__FILE__, ".php"),
                    'Message' => "Creazione Istituto: {$institute->getName()};",
                    'Context' => print_r($data, true)
                ));

                $response->setSuccess(true);
                $response->results = $institute->toArray(BasePeer::TYPE_FIELDNAME);
            } else {
                $response->message = "Save failed";
            }
        }

        return new JsonModel((array) $response);
    }

    /**
     *
     * @param int $id
     * @param array $data
     * @return JsonModel
     */
    public function update($id, $data) {
        $response = new Response;

        if ($this->session_auth['uid'] > 0 || $this->fromLocal) {
            $institute = InstituteQuery::create()->findOneById($id);

            if ($institute !== null) {
                $oldName = $institute->getName();

                $institute->set();
                $institute->set();
                $institute->set();
                $institute->set();
                $institute->set();
                $institute->set();
                $institute->set();
                $institute->set();
                $institute->set();
                $institute->set();

                try {
                    $institute->save();

                    // Logging
                    $this->logger->log(array(
                        'Type'    => 'INFO',
                        'Scope'   => __FILE__,
                        'Event'   => basename(__FILE__, ".php"),
                        'Message' => "Aggiornamento Istituto: {$oldName} -> {$institute->getName()}",
                        'Context' => print_r($institute, true)
                    ));

                    $response->setSuccess(true);
                    $response->results = $institute->toArray(BasePeer::TYPE_FIELDNAME);
                } catch (Exception $e) {
                    $response->message = 'Cannot update Institute';
                }
            } else {
                $response->message = 'Institute not found';
            }
        }

        return new JsonModel((array) $response);
    }

    /**
     *
     * @param int $id
     * @return JsonModel
     */
    public function delete($id) {
        $response = new Response;

        if ($this->session_auth['uid'] > 0) {
            $institute = InstituteQuery::create()->findOneById($id);

            if ($institute !== null) {
                $institute->delete();

                // Logging
                $this->logger->log(array(
                    'Type'    => 'INFO',
                    'Scope'   => __FILE__,
                    'Event'   => basename(__FILE__, ".php"),
                    'Message' => "Eliminazione Istituto: {$institute->getId()} - {$institute->getName()}",
                    'Context' => print_r($institute, true)
                ));

                $response->setSuccess(true);
                $response->results = $institute->toArray(BasePeer::TYPE_FIELDNAME);
            } else {
                $response->message = 'Institute not found';
            }
        }

        return new JsonModel((array) $response);
    }

}
