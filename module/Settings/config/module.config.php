<?php

/**
 * Zend Framework (http://framework.zend.com/)
 *
 * @link      http://github.com/zendframework/ZendSkeletonApplication for the canonical source repository
 * @copyright Copyright (c) 2005-2014 Zend Technologies USA Inc. (http://www.zend.com)
 * @license   http://framework.zend.com/license/new-bsd New BSD License
 */
return [
    'controllers'     => [
        'invokables' => [
            'Settings\Print' => 'Settings\Controller\PrintController',
            'Settings\Institute' => 'Settings\Controller\InstituteController'
        ]
    ],
    'router'          => [
        'routes' => [
            'settings' => [
                'type'          => 'Literal',
                'options'       => [
                    'route' => '/settings'
                ],
                'may_terminate' => false,
                'child_routes'  => [
                    'print' => [
                        'type'    => 'Segment',
                        'options' => [
                            'route'       => '/print[/:id]',
                            'constraints' => [
                                'id' => '\d+'
                            ],
                            'defaults'    => [
                                'controller' => 'Settings\Print'
                            ]
                        ]
                    ],
                    'institute_mc' => [
                        'type'    => 'Segment',
                        'options' => [
                            'route'       => '/institute[/:id]',
                            'constraints' => [
                                'id' => '\d+'
                            ],
                            'defaults'    => [
                                'controller' => 'Settings\Institute'
                            ]
                        ]
                    ]
                ]
            ]
        ]
    ],
    'view_manager'    => [
        'strategies' => [
            'ViewJsonStrategy'
        ]
    ],
    'service_manager' => [
        'abstract_factories' => [
            'Zend\Cache\Service\StorageCacheAbstractServiceFactory',
            'Zend\Log\LoggerAbstractServiceFactory'
        ],
        'aliases'            => [
            'translator' => 'MvcTranslator'
        ]
    ],
    'translator'      => [
        'locale'                    => 'it_IT',
        'translation_file_patterns' => [
            [
                'type'     => 'gettext',
                'base_dir' => __DIR__ . '/../language',
                'pattern'  => '%s.mo'
            ]
        ]
    ],
    'console'         => [
        'router' => [
            'routes' => [
            ]
        ]
    ]
];
