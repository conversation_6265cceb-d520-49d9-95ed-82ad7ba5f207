<?php

namespace Protocol\Controller;

use MT\Logger\LoggerZend,
    MT\Sencha\Response,
    Database\Archive\DocumentQuery,
    Database\Protocol\CorrespondentQuery,
    Database\Core\ParameterQuery,
    Database\Protocol\Protocol,
    Database\Protocol\ProtocolCorrespondent,
    Database\Protocol\ProtocolDocument,
    Database\Protocol\ProtocolHistory,
    Database\Protocol\ProtocolProtocol,
    Database\Protocol\ProtocolQuery,
    Database\Protocol\ProtocolCorrespondentQuery,
    Database\Protocol\ProtocolDocumentQuery,
    Database\Protocol\ProtocolHistoryQuery,
    Database\Protocol\ProtocolProtocolQuery,
    Database\Protocol\ProtocolViewQuery,
    Database\Protocol\RegisterQuery,
    Database\Core\CoreForcedActionQuery,
    Database\Setting,
    Database\Setting\UserQuery,
    Database\Archive\ArchiveDocumentFileQuery,
    Core\Model\Session\Session,
    BasePeer,
    Zend\Mvc\Controller\AbstractRestfulController,
    Zend\View\Model\JsonModel;


class ProtocolController extends AbstractRestfulController {

    protected $logger;
    protected $session_auth;

    public function __construct() {

        $this->logger = new LoggerZend;
        $this->session_auth = Session::readSessionAuth();
    }

    /**
     *
     * @return JsonModel
     */
    public function getList() {
        $response = new Response;
        if ($this->session_auth['uid'] > 0) {
            $response->setSuccess(true);

            $filter = $this->params()->fromQuery();            
            
            
            $user = UserQuery::create()->findOneById( $this->session_auth['uid']);

            $protocols = ProtocolViewQuery::create()->getList($filter,[], $this->protocolPermissions($user, array( 'reserved' => 't' )) );
            
            $response->total = $protocols['count'];
            $response->results = empty($protocols['protocols']) ? [] : $protocols['protocols'];
        }

        return new JsonModel((array) $response);
    }

    /**
     *
     * @param int $id
     * @return JsonModel
     */
    public function get($id) {
        $response = new Response;
        if ($this->session_auth['uid'] > 0) {

            $protocol = ProtocolViewQuery::create()->findOneById($id);

            if ($protocol !== null) {
                if ($protocol->getReserved() ) {
                    $data[ 'reserved' ] = 't';
                }
                $user = UserQuery::create()->findOneById( $this->session_auth['uid'] );
                if ( ! $this->protocolPermissions( $user, $data) ){
                    $response->setStatus(403);
                    $response->setSuccess(false);
                    $response->message = "Non hai i permessi necessari per visualizzare protocolli. "
                        . "Utilizzare un utente abilitato oppure abilitare il permesso di aggiunta nella sezione protocolli all'interno dei gruppi.";
                    return new JsonModel((array) $response);
                }
                $response->setSuccess(true);

                $response->results = $protocol->toArray(BasePeer::TYPE_FIELDNAME);
                $response->results['date'] = date("c", $response->results['date']);
                $response->results['linked_correspondents'] = explode(',', $response->results['linked_correspondents']);
                $response->results['linked_documents'] = explode(',', $response->results['linked_documents']);
                $response->results['linked_protocols'] = explode(',', $response->results['linked_protocols']);
            }
        }

        return new JsonModel((array) $response);
    }

    /**
     *
     * @param array $data
     * @return JsonModel
     */
    public function create($data) {
        $response = new Response;

        if ($this->session_auth['uid'] > 0) {

            // Check protocol permission
            $user = UserQuery::create()->findOneById($this->session_auth['uid']);
            if ( ! $this->protocolPermissions( $user, $data) ){
                $response->setStatus(403);
                $response->setSuccess(false);
                $response->message = "Non hai i permessi necessari per creare protocolli. "
                        . "Utilizzare un utente abilitato oppure abilitare il permesso di aggiunta nella sezione protocolli all'interno dei gruppi.";
                return new JsonModel((array) $response);
            }
            /*
            if (!$user->hasPermission(401)) {
                $response->setStatus(403);
                $response->setSuccess(false);
                $response->message = "Non hai i permessi necessari per creare protocolli. "
                        . "Utilizzare un utente abilitato oppure abilitare il permesso di aggiunta nella sezione protocolli all'interno dei gruppi.";
                return new JsonModel((array) $response);
            }

            if ( $data['reserved'] && ! $user->hasPermission(470)) {
                $response->setStatus(403);
                $response->setSuccess(false);
                $response->message = "Non hai i permessi necessari per creare protocolli riservati.";
                return new JsonModel((array) $response);
            }
            */
            $filter = $this->params()->fromQuery();
            unset($data['id']);
            // if ($data['date'] === null ) {
            //     $data['date'] = time();
            // }
            $data['date'] = time();
            
            $register = RegisterQuery::create()->findOneByCode(Date('Ymd', $data['date']));
            if ( is_object( $register ) ) {
                $response->setSuccess(false);
                $response->setStatus(403);
                $response->message = "Non è possibile creare un protocollo perché esiste già il registro per la data selezionata";
                return new JsonModel((array) $response);
            }

            // Check if protocol is created with date
            $last_protocol = ProtocolQuery::create()->orderByDate(\Criteria::DESC)->findOne();
            if ($last_protocol && empty($filter['force_period_warning'])) {
                if ($last_protocol->getDate() > $data['date']) {
                    $response->setSuccess(false);
                    $response->warning = true;                    
                    $response->message = 'Il protocollo che si sta tentando di inserire è antecedente all\'ultimo inserito. '
                            . 'Questa operazione potrebbe causare una numerazione non congruente con le date di inserimento. '
                            . 'Confermate l\'operazione?';
                    return new JsonModel((array) $response);
                }
            }
            // Checks for max protocol number in the year
            $year = date("Y", $data['date']);
            $start = strtotime("1-1-{$year}");
            $end = strtotime("1-1-{$year} + 1 year");

            $maxProtocol = ProtocolQuery::create()->filterByDate(array('min' => $start, 'max' => $end))->orderByProtocolNumber(\Criteria::DESC)->findOne();

            // Sets the number to 1 if the protocol is the first of the year
            $data['protocol_number'] = 1;
            if ($maxProtocol) {
                $data['protocol_number'] += $maxProtocol->getProtocolNumber();
            }
            
            // Creates the new protocol
            $protocol = new Protocol;

            $txt = [];
            foreach ((array) $data['linked_correspondents'] as $cId) {
                $correspondent = CorrespondentQuery::create()->findOneById($cId);
                if ($correspondent !== null) {
                    $txt[] = $correspondent->getTitle();
                }
            }
            $data_header = $data['date'];
            $protocol->setProtocolNumber($data['protocol_number']);
            $protocol->setDate($data['date']);
            $protocol->setDescription($data['description'] ? $data['description'] : null);
            $protocol->setDirection($data['direction']);
            $protocol->setTypeId($data['type_id'] ? $data['type_id'] : null);
            $protocol->setSubjectKindId($data['subject_kind_id'] ? $data['subject_kind_id'] : null);
            $protocol->setSendMethodId($data['send_method_id'] ? $data['send_method_id'] : null);
            $protocol->setDossier($data['dossier'] ? $data['dossier'] : null);
            $protocol->setNote($data['note'] ? $data['note'] : null);
            $protocol->setCorrespondentsText(implode(" || ", $txt));
            $protocol->setExternalActNumber($data['external_act_number'] ? $data['external_act_number'] : null);
            $protocol->setHeaderPosition($data['header_position'] ? $data['header_position'] : 'top' );

            // NEW VERSION
            // $protocol->setMechanCode($data['mechan_code'] ? $data['mechan_code'] : null);
            if ( isset($data[ 'reserved'] ) ){
                $protocol->setReserved($data['reserved']);
            } else {
                $protocol->setReserved(false);
            }

            if ($protocol->save() > 0) {                
                foreach ((array) $data['linked_correspondents'] as $cId) {
                    $link = new ProtocolCorrespondent;
                    $link->setProtocolId($protocol->getId());
                    $link->setCorrespondentId($cId);

                    if ($link->save() > 0) {
                        $this->logger->log(array(
                            'Type'    => 'INFO',
                            'Scope'   => __FILE__,
                            'Event'   => basename(__FILE__, ".php"),
                            'Message' => "Creazione Link Protocollo-Corrispondente: {$protocol->getId()} - {$link->getCorrespondentId()};",
                            'Context' => print_r($data, true)
                        ));
                    } else {
                        $this->logger->log(array(
                            'Type'    => 'INFO',
                            'Scope'   => __FILE__,
                            'Event'   => basename(__FILE__, ".php"),
                            'Message' => "Fallita Creazione Link Protocollo-Corrispondente: {$protocol->getId()} - {$link->getCorrespondentId()};",
                            'Context' => print_r($data, true)
                        ));
                    }
                }


                $doc = null;
                // Set archive document check or, in case, build archive document
                if (count($data['linked_documents']) > 0) {
                    $document = $data['linked_documents'][0];
                    $docFile = ArchiveDocumentFileQuery::create()->findOneById($document);
                    if (is_object( $docFile) ) {
                        $doc = DocumentQuery::create()->findOneById($docFile->getArchiveDocument());
                    }

                    if($doc){
                        CoreForcedActionQuery::create()->checkProtocolStepForcing($doc, (int) $this->session_auth['uid']);
                        $doc->setActionProtocolDate(time());

                        $step = \Database\Archive\DocumentQuery::create()->getCurrentStep($doc, true);
                        if($step) {
                            if($step->getType() === 'U'){
                                $doc->setAssignToUser($step->getUserId());
                                $doc->setAssignToOffice(NULL);
                            } else {
                                $doc->setAssignToOffice($step->getUserId());
                                $doc->setAssignToUser(NULL);
                            }

                        }


                        $doc->save();
                        // ------------------------------- Log ------------------------------------ //
                        $userName = \Database\Setting\UserQuery::create()->findOneById($this->session_auth['uid'])->getUserName();
                        $logDescription = "L'utente {$userName} ha creato il protocollo relativo al fascicolo";
                        $log = new \Database\Archive\ArchiveLog;
                        $log->setDate(date('c'));
                        $log->setUser($this->session_auth['uid']);
                        $log->setDocument($doc->getId());
                        $log->setDescription($logDescription);
                        $log->save();
                        // ------------------------------- Log ------------------------------------ //
                    }
                }

                // Creates the links to the documents
                $config = $this->getServiceLocator()->get('Config');
                foreach ((array) $data['linked_documents'] as $document) {

                    $link = new ProtocolDocument;
                    $link->setProtocolId($protocol->getId());
                    $link->setDocumentId($document);

                    $file = ArchiveDocumentFileQuery::create()->findOneById($document);
                    $source = $config['PATHS']['ARCHIVE']['DOCUMENTS'] . "/" . $file->getPath();
                    if(mime_content_type($source) == 'application/pdf'){
                        # Generate a copy in original folder
                        $dest = dirname($config['PATHS']['ARCHIVE']['PR_FILE_ORIGINAL'] . "/" . $file->getPath());

                        if (!file_exists($dest)) {
                            mkdir($dest, 0770, true);
                        }
                        $dest = $config['PATHS']['ARCHIVE']['PR_FILE_ORIGINAL'] . "/" . $file->getPath();

                        copy($source, $dest );

                        # Apply note to uploaded document
                        $protocolComplete = \Database\Protocol\ProtocolViewQuery::create()->findOneById($protocol->getId());
                        $titCode = $protocolComplete->getTypeText();
                        
                        // NEW VERSION
                        // $meccCode = $protocol->getMechanCode() ? "Cod. meccanografico: " . $protocol->getMechanCode() : "";
                        $institute = Setting\InstituteQuery::create()->findOneByActive(true);
                        $meccCode = '';
                        if (is_object($institute) ) {
                            $meccCode = Setting\InstituteQuery::create()->findOneByActive(true)->getMeccanographicCode();
                        }
                        $meccCode = $meccCode ? "Cod. meccanografico: " . $meccCode : "";
                        
                        $pdf = new \Protocol\PrintPDF\HeadeProtocolPDF;
                        $pageCount = $pdf->setSourceFile($source);
                        for ($pageNo=1; $pageNo<=$pageCount; $pageNo++) {
                            $idx = $pdf->importPage($pageNo);
                            $size = $pdf->getTemplateSize($idx);

                            $orientation = 'P';
                            $bottomPosition = 290;
                            if ( is_array( $size )) {
                                $bottomPosition = $size['h'] - 7;
                                if ($size['w'] > $size[ 'h']){
                                    $orientation = 'L';
                                }
                            }
                            $headerPosition = $data['header_position'] == 'bottom' ? $bottomPosition : 0;
                            $pdf->AddPage($orientation, array($size['w'], $size['h']));

                            $pdf->useTemplate($idx);
                            $pdf->SetAutoPageBreak(false);

                            $pdf->setY($headerPosition);
                            
                            $this->logger->log(array(
                            'Type'    => 'INFO',
                                'Scope'   => __FILE__,
                                'Event'   => basename(__FILE__, ".php"),
                                'Message' => "Scrittura Header: {$protocol->getId()}  del {$data_header};",
                                'Context' => print_r($data, true)
                            ));
                            $aooCode = ParameterQuery::create()->findOneByName('PROTOCOL_AOO_CODE')->getValue();
                            $headerText = ParameterQuery::create()->findOneByName('PROTOCOL_HEADER')->getValue();
                            $headerText = str_replace(
                                ['{codice_meccanografico}', '{numero_protocollo}', '{anno_breve}','{data}', '{titolario}', '{codice_aoo}'],
                                [$meccCode, $data['protocol_number'], date('y', $data_header), date('d-m-Y', $data_header), $titCode, $aooCode],
                                $headerText);

                            $this->logger->log(array(
                            'Type'    => 'INFO',
                                'Scope'   => __FILE__,
                                'Event'   => basename(__FILE__, ".php"),
                                'Message' => "Header: {$headerText};",
                                'Context' => print_r($data, true)
                            ));
                            $pdf->Cell(0, 0, $headerText, 0, 0, 'R');


                            # $headerText = $meccCode . " Prot. n " . $protocol->getProtocolNumber() . ' del ' . date('d-m-Y', $protocol->getDate()) . ' - Tit. ' . $titCode;

                        }
                        $pdf->Output($source, 'F');
                    }

                    if ($link->save() > 0) {
                        $this->logger->log(array(
                            'Type'    => 'INFO',
                            'Scope'   => __FILE__,
                            'Event'   => basename(__FILE__, ".php"),
                            'Message' => "Creazione Link Protocollo-Documento: {$protocol->getId()} - {$link->getDocumentId()};",
                            'Context' => print_r($data, true)
                        ));
                    } else {
                        $this->logger->log(array(
                            'Type'    => 'INFO',
                            'Scope'   => __FILE__,
                            'Event'   => basename(__FILE__, ".php"),
                            'Message' => "Fallita Creazione Link Protocollo-Documento: {$protocol->getId()} - {$link->getDocumentId()};",
                            'Context' => print_r($data, true)
                        ));
                    }
                }

                // Creates the links to the protocols
                foreach ((array) $data['linked_protocols'] as $p) {
                    $link = new ProtocolProtocol;
                    $link->setProtocol1Id($protocol->getId());
                    $link->setProtocol2Id($p);

                    if ($link->save() > 0) {
                        $this->logger->log(array(
                            'Type'    => 'INFO',
                            'Scope'   => __FILE__,
                            'Event'   => basename(__FILE__, ".php"),
                            'Message' => "Creazione Link Protocollo-Protocolo: {$protocol->getId()} - {$link->getProtocol2Id()};",
                            'Context' => print_r($data, true)
                        ));
                    } else {
                        $this->logger->log(array(
                            'Type'    => 'INFO',
                            'Scope'   => __FILE__,
                            'Event'   => basename(__FILE__, ".php"),
                            'Message' => "Fallita Creazione Link Protocollo-Protocollo: {$protocol->getId()} - {$link->getProtocol2Id()};",
                            'Context' => print_r($data, true)
                        ));
                    }
                }

                // Writes Protocol History Row
                $history = new ProtocolHistory;
                $history->setAsInsertionRow($protocol->getId(), $this->session_auth['uid']);

                if ($history->save() > 0) {
                    $this->logger->log(array(
                        'Type'    => 'INFO',
                        'Scope'   => __FILE__,
                        'Event'   => basename(__FILE__, ".php"),
                        'Message' => "Inserita riga storico Protocollo: {$protocol->getId()} - I;",
                        'Context' => print_r($data, true)
                    ));
                } else {
                    $this->logger->log(array(
                        'Type'    => 'INFO',
                        'Scope'   => __FILE__,
                        'Event'   => basename(__FILE__, ".php"),
                        'Message' => "Fallito Inserimento riga storico Protocollo: {$protocol->getId()} - I;",
                        'Context' => print_r($data, true)
                    ));
                }

                // Writes Protocol History Row - Forced
                if (isset($filter['forced'])) {
                    $historyF = new ProtocolHistory;
                    $historyF->setAsForcedRow($protocol->getId(), json_decode($filter['forced'], true), $this->session_auth['uid']);
                    if ($historyF->save() > 0) {
                        $this->logger->log(array(
                            'Type'    => 'INFO',
                            'Scope'   => __FILE__,
                            'Event'   => basename(__FILE__, ".php"),
                            'Message' => "Inserita riga storico Protocollo: {$protocol->getId()} - F;",
                            'Context' => print_r($data, true)
                        ));
                    } else {
                        $this->logger->log(array(
                            'Type'    => 'INFO',
                            'Scope'   => __FILE__,
                            'Event'   => basename(__FILE__, ".php"),
                            'Message' => "Fallito Inserimento riga storico Protocollo: {$protocol->getId()} - F;",
                            'Context' => print_r($data, true)
                        ));
                    }
                }

                $this->logger->log(array(
                    'Type'    => 'INFO',
                    'Scope'   => __FILE__,
                    'Event'   => basename(__FILE__, ".php"),
                    'Message' => "Creazione Protocollo: {$protocol->getProtocolNumber()};",
                    'Context' => print_r($data, true)
                ));

                $response->setSuccess(true);
                $response->results = $protocol->toArray(BasePeer::TYPE_FIELDNAME);
            } else {
                $response->message = "Protocollo non salvato";
            }
        }

        return new JsonModel((array) $response);
    }

    public function linkProtocolsAction() {
        $protocolId = $this->params()->fromRoute('id');
        $protocols = json_decode($this->params()->fromPost('protocols'), true);
        $response = new Response;
        
        if ($this->session_auth['uid'] > 0) {
            // Removes old links to the protocols
            ProtocolProtocolQuery::create()->filterByProtocol1Id($protocolId)->delete();
            ProtocolProtocolQuery::create()->filterByProtocol2Id($protocolId)->delete();
            // Creates the links to the protocols
            foreach ((array) $protocols as $p) {
                $link = new ProtocolProtocol;
                $link->setProtocol1Id($protocolId);
                $link->setProtocol2Id($p);

                if ($link->save() > 0) {
                    $this->logger->log(array(
                        'Type'    => 'INFO',
                        'Scope'   => __FILE__,
                        'Event'   => basename(__FILE__, ".php"),
                        'Message' => "Creazione Link Protocollo-Protocolo: {$protocolId} - {$link->getProtocol2Id()};",
                        'Context' => print_r($protocols, true)
                    ));
                } else {
                    $this->logger->log(array(
                        'Type'    => 'INFO',
                        'Scope'   => __FILE__,
                        'Event'   => basename(__FILE__, ".php"),
                        'Message' => "Fallita Creazione Link Protocollo-Protocollo: {$protocolId} - {$link->getProtocol2Id()};",
                        'Context' => print_r($protocols, true)
                    ));
                }
            }

            $response->setSuccess(true);
        }

        return new JsonModel((array) $response);
    }

    /**
     *
     * @param int $id
     * @param array $data
     * @return JsonModel
     */
    public function update($id, $data) {
        $response = new Response;
        
        if ($this->session_auth['uid'] > 0) {

            if($data['canceled'] !== true){
                $response->setSuccess(false);
                $response->setStatus(403);
                $response->message = "Non è possibile modificare un protocollo già inserito";
                return new JsonModel((array) $response);
            } 
            
            $config = $this->getServiceLocator()->get('Config');
            $protocol = ProtocolQuery::create()->findOneById($id);

            if ($protocol !== null) {                
                $user = UserQuery::create()->findOneById($this->session_auth['uid']);
                $protocoldata = $data;
                if ($protocol->getReserved()){
                    $protocoldata[ 'reserved' ] = 't';
                }
                if ( ! $this->protocolPermissions( $user, $protocoldata  )){
                    $response->setStatus(403);
                    $response->setSuccess(false);
                    $response->message = "Non hai i permessi necessari per modificare protocolli. "
                            . "Utilizzare un utente abilitato oppure abilitare il permesso di aggiunta nella sezione protocolli all'interno dei gruppi.";
                    return new JsonModel((array) $response);
                }
                
                
                // Se esiste un registro per la data del protocollo, non è possibile cancellare il protocollo
                $register = RegisterQuery::create()->findOneByCode(Date('Ymd', $protocol->getDate()));
                
                
                if ( !$data['canceled'] && is_object( $register ) && $protocol->getProtocolNumber() >= $register->getFirstRegistrationNumber() && $protocol->getProtocolNumber() <= $register->getLastRegistrationNumber() ) {
                    $response->setSuccess(false);
                    $response->setStatus(403);
                    $response->message = "Non è possibile modificare un protocollo già inserito nel registro di protocollo";
                    return new JsonModel((array) $response);
                }
                $filter = $this->params()->fromQuery();
                $oldCanceled = $protocol->getCanceled();
                $newCancelled = $data['canceled'];
                unset($data['canceled']);

                if ( isset($data[ 'reserved'] ) ){
                    $protocol->setReserved($data['reserved']);
                } else {
                    $protocol->setReserved(false);
                }

                if ($oldCanceled != $newCancelled) {

                    $protocol->setCanceled($newCancelled);
                } else {

                    $txt = [];
                    foreach ((array) $data['linked_correspondents'] as $cId) {
                        $correspondent = CorrespondentQuery::create()->findOneById($cId);
                        if ($correspondent !== null) {
                            $txt[] = $correspondent->getTitle();
                        }
                    }

                    if($data['date']) {
                       $data['date'] = strtotime($data['date']);
                    }
                    if(count($txt) > 0) {
                       $data['correspondents_text'] = implode(" || ", $txt);
                    }

                    $protocol->fromArray($data, \BasePeer::TYPE_FIELDNAME);

                    // Removes old links to the correspondents
                    ProtocolCorrespondentQuery::create()->filterByProtocolId($protocol->getId())->delete();

                    // Creates the links to the correspondents
                    foreach ((array) $data['linked_correspondents'] as $cId) {
                        $link = new ProtocolCorrespondent;
                        $link->setProtocolId($protocol->getId());
                        $link->setCorrespondentId($cId);

                        if ($link->save() > 0) {
                            $this->logger->log(array(
                                'Type'    => 'INFO',
                                'Scope'   => __FILE__,
                                'Event'   => basename(__FILE__, ".php"),
                                'Message' => "Creazione Link Protocollo-Corrispondente: {$protocol->getId()} - {$link->getCorrespondentId()};",
                                'Context' => print_r($data, true)
                            ));
                        } else {
                            $this->logger->log(array(
                                'Type'    => 'INFO',
                                'Scope'   => __FILE__,
                                'Event'   => basename(__FILE__, ".php"),
                                'Message' => "Fallita Creazione Link Protocollo-Corrispondente: {$protocol->getId()} - {$link->getCorrespondentId()};",
                                'Context' => print_r($data, true)
                            ));
                        }
                    }

                    if ( is_array( $data['linked_documents'] ) ) {

                        $linksStart = ProtocolDocumentQuery::create()->select(['document_id'])->filterByProtocolId($id)->find()->toArray();
                        $linksAdd = array_diff($data['linked_documents'], $linksStart);
                        $linksRemove = array_diff($linksStart, $data['linked_documents']);


                        // Delete
                        if(count($linksRemove) > 0) {
                            ProtocolDocumentQuery::create()->filterByProtocolId($id)->filterByDocumentId($linksRemove, \Criteria::IN)->delete();
                            \Database\Archive\ArchiveDocumentFileQuery::create()->filterById($linksRemove, \Criteria::IN)->delete();
                        }

                        // Creates the links to the documents

                        foreach ((array) $linksAdd as $document) {

                            $link = new ProtocolDocument;
                            $link->setProtocolId($protocol->getId());
                            $link->setDocumentId($document);

                            $file = ArchiveDocumentFileQuery::create()->findOneById($document);
                            $source = $config['PATHS']['ARCHIVE']['DOCUMENTS'] . "/" . $file->getPath();
                            if(mime_content_type($source) == 'application/pdf'){
                                # Generate a copy in original folder
                                $dest = dirname($config['PATHS']['ARCHIVE']['PR_FILE_ORIGINAL'] . "/" . $file->getPath());
                                if (!file_exists($dest)) {
                                    mkdir($dest, 0770, true);
                                }
                                copy($source, $dest . '/' . $file->getFileName());

                                # Apply note to uploaded document
                                $protocolComplete = \Database\Protocol\ProtocolViewQuery::create()->findOneById($protocol->getId());
                                $titCode = $protocolComplete->getTypeText();
                                $meccCode = Setting\InstituteQuery::create()->findOneByActive(true)->getMeccanographicCode();
                                $meccCode = $meccCode ? "Cod. meccanografico: " . $meccCode : "";
                                $pdf = new \FPDI;
                                $pageCount = $pdf->setSourceFile($source);
                                for ($pageNo=1; $pageNo<=$pageCount; $pageNo++) {
                                    if ( 0 ) {
                                        $idx = $pdf->importPage($pageNo);
                                        $size = $pdf->getTemplateSize($templateId);
                                        $pdf->AddPage('P', array($size['w'], $size['h']));
                                        $pdf->useTemplate($idx);
                                        $pdf->setY(0);
                                    } else {
                                        $orientation = 'P';
                                        $bottomPosition = 290;
                                        $idx = $pdf->importPage($pageNo);
                                        $size = $pdf->getTemplateSize($idx);

                                        if ( is_array( $size ) && ( $size['w'] > $size[ 'h']) ){
                                            $orientation = 'L';
                                            $bottomPosition = $size['h'] - 7;
                                        }
                                        $headerPosition = $data['header_position'] == 'bottom' ? $bottomPosition : 0;
                                        $pdf->AddPage($orientation, array($size['w'], $size['h']));
                                        $pdf->useTemplate($idx);
                                        $pdf->SetAutoPageBreak(false);

                                        $pdf->setY($headerPosition);
                                    }

                                    $aooCode = ParameterQuery::create()->findOneByName('PROTOCOL_AOO_CODE')->getValue();
                                    $headerText = ParameterQuery::create()->findOneByName('PROTOCOL_HEADER')->getValue();
                                    $headerText = str_replace(
                                        ['{codice_meccanografico}', '{numero_protocollo}', '{anno_breve}','{data}', '{titolario}', '{codice_aoo}'],
                                        [$meccCode, $data['protocol_number'], date('y', $protocol->getDate()), date('d-m-Y', $protocol->getDate()), $titCode, $aooCode],
                                        $headerText);

                                    $pdf->Cell(0, 0, $headerText, 0, 0, 'R');
                                    //$pdf->Cell(0, 0, $meccCode . " Prot. n " . $protocol->getProtocolNumber() . ' del ' . date('d-m-Y', $protocol->getDate()) . ' - Tit. ' . $titCode, 0, 0, 'R');
                                }
                                $pdf->Output($source, 'F');
                            }


                            if ($link->save() > 0) {
                                $this->logger->log(array(
                                    'Type'    => 'INFO',
                                    'Scope'   => __FILE__,
                                    'Event'   => basename(__FILE__, ".php"),
                                    'Message' => "Creazione Link Protocollo-Documento: {$protocol->getId()} - {$link->getDocumentId()};",
                                    'Context' => print_r($data, true)
                                ));
                            } else {
                                $this->logger->log(array(
                                    'Type'    => 'INFO',
                                    'Scope'   => __FILE__,
                                    'Event'   => basename(__FILE__, ".php"),
                                    'Message' => "Fallita Creazione Link Protocollo-Documento: {$protocol->getId()} - {$link->getDocumentId()};",
                                    'Context' => print_r($data, true)
                                ));
                            }
                        }
                    }
                    // Removes old links to the protocols
                    ProtocolProtocolQuery::create()->filterByProtocol1Id($protocol->getId())->delete();
                    ProtocolProtocolQuery::create()->filterByProtocol2Id($protocol->getId())->delete();
                    // Creates the links to the protocols
                    foreach ((array) $data['linked_protocols'] as $p) {
                        $link = new ProtocolProtocol;
                        $link->setProtocol1Id($protocol->getId());
                        $link->setProtocol2Id($p);

                        if ($link->save() > 0) {
                            $this->logger->log(array(
                                'Type'    => 'INFO',
                                'Scope'   => __FILE__,
                                'Event'   => basename(__FILE__, ".php"),
                                'Message' => "Creazione Link Protocollo-Protocolo: {$protocol->getId()} - {$link->getProtocol2Id()};",
                                'Context' => print_r($data, true)
                            ));
                        } else {
                            $this->logger->log(array(
                                'Type'    => 'INFO',
                                'Scope'   => __FILE__,
                                'Event'   => basename(__FILE__, ".php"),
                                'Message' => "Fallita Creazione Link Protocollo-Protocollo: {$protocol->getId()} - {$link->getProtocol2Id()};",
                                'Context' => print_r($data, true)
                            ));
                        }
                    }
                }

                try {
                    $protocol->save();
                    // Writes Protocol History Row
                    $history = new ProtocolHistory;

                    if ($newCancelled) {
                        $history->setAsCancelationRow($protocol->getId(), $this->session_auth['uid']);
                    } else {
                        $history->setAsEditRow($protocol->getId(), $this->session_auth['uid']);
//                        if ($data['reserved'] != $oldReserved) {
//                            $history->setAsReservedRow($protocol->getId(), $data['reserved'], $this->session_auth['uid']);
//                        }
                    }

                    if ($history->save() > 0) {
                        $this->logger->log(array(
                            'Type'    => 'INFO',
                            'Scope'   => __FILE__,
                            'Event'   => basename(__FILE__, ".php"),
                            'Message' => "Inserita riga storico Protocollo: {$protocol->getId()} - E;",
                            'Context' => print_r($data, true)
                        ));
                    } else {
                        $this->logger->log(array(
                            'Type'    => 'INFO',
                            'Scope'   => __FILE__,
                            'Event'   => basename(__FILE__, ".php"),
                            'Message' => "Fallito Inserimento riga storico Protocollo: {$protocol->getId()} - E;",
                            'Context' => print_r($data, true)
                        ));
                    }

                    // Writes Protocol History Row - Forced
                    if (isset($filter['forced'])) {
                        $historyF = new ProtocolHistory;
                        $historyF->setAsForcedRow($protocol->getId(), json_decode($filter['forced'], true), $this->session_auth['uid']);
                        if ($historyF->save() > 0) {
                            $this->logger->log(array(
                                'Type'    => 'INFO',
                                'Scope'   => __FILE__,
                                'Event'   => basename(__FILE__, ".php"),
                                'Message' => "Inserita riga storico Protocollo: {$protocol->getId()} - F;",
                                'Context' => print_r($data, true)
                            ));
                        } else {
                            $this->logger->log(array(
                                'Type'    => 'INFO',
                                'Scope'   => __FILE__,
                                'Event'   => basename(__FILE__, ".php"),
                                'Message' => "Fallito Inserimento riga storico Protocollo: {$protocol->getId()} - F;",
                                'Context' => print_r($data, true)
                            ));
                        }
                    }

                    $this->logger->log(array(
                        'Type'    => 'INFO',
                        'Scope'   => __FILE__,
                        'Event'   => basename(__FILE__, ".php"),
                        'Message' => "Aggiornamento Protocollo: {$protocol->getId()} - {$protocol->getProtocolNumber()}",
                        'Context' => print_r($data, true)
                    ));

                    $response->setSuccess(true);
                    $response->results = $protocol->toArray(BasePeer::TYPE_FIELDNAME);
                } catch (Exception $e) {
                    $response->message = "Save failed";
                }
            }
        }

        return new JsonModel((array) $response);
    }

    /**
     *
     * @param int $id
     * @return JsonModel
     */
    public function delete($id) {
        $response = new Response;

        if ($this->session_auth['uid'] > 0) {
            $user = UserQuery::create()->findOneById($this->session_auth['uid']);
            $protocol = ProtocolQuery::create()->findOneById($id);

            if ($protocol !== null) {
                $user = UserQuery::create()->findOneById($this->session_auth['uid']);
                $data = [];
                if ( $protocol->getReserved()){
                    $data['reserved'] = 't';
                }
                if ( ! $this->protocolPermissions( $user, $data) ){
                    $response->setStatus(403);
                    $response->setSuccess(false);
                    $response->message = "Non hai i permessi necessari per eliminare protocolli. "
                            . "Utilizzare un utente abilitato oppure abilitare il permesso di aggiunta nella sezione protocolli all'interno dei gruppi.";
                    return new JsonModel((array) $response);
                }
                // Writes Protocol History Row
                $history = new ProtocolHistory;
                $history->setAsDeletionRow($protocol->getId(), $this->session_auth['uid']);

                if ($history->save() > 0) {
                    $this->logger->log(array(
                        'Type'    => 'INFO',
                        'Scope'   => __FILE__,
                        'Event'   => basename(__FILE__, ".php"),
                        'Message' => "Inserita riga storico Protocollo: {$protocol->getId()} - D;",
                        'Context' => print_r($protocol, true)
                    ));

                    $protocol->delete();

                    $this->logger->log(array(
                        'Type'    => 'INFO',
                        'Scope'   => __FILE__,
                        'Event'   => basename(__FILE__, ".php"),
                        'Message' => "Eliminazione Protocollo: {$protocol->getId()} - {$protocol->getProtocolNumber()}",
                        'Context' => print_r($protocol, true)
                    ));

                    $response->setSuccess(true);
                    $response->results = $protocol->toArray(BasePeer::TYPE_FIELDNAME);
                }
            }
        }

        return new JsonModel((array) $response);
    }

    /**
     *
     * @return JsonModel
     */
    public function historyAction() {
        $response = new Response;

        if ($this->session_auth['uid'] > 0) {
            $response->setSuccess(true);
            $response->results = [];

            $records = ProtocolHistoryQuery::create()->findByProtocolId(filter_input(INPUT_GET, 'protocol'));

            foreach ($records as $key => $record) {
                $response->results[$key] = $record->toArray(BasePeer::TYPE_FIELDNAME);

                $response->results[$key]['date'] = date("c", $response->results[$key]['date']);

                switch ($response->results[$key]['action']) {
                    case 'I':
                        $response->results[$key]['action'] = 'Inserito';
                        break;
                    case 'E':
                        $response->results[$key]['action'] = 'Modificato';
                        break;
                    case 'D':
                        $response->results[$key]['action'] = 'Eliminato';
                        break;
                    case 'C':
                        $response->results[$key]['action'] = 'Annullato';
                        break;
                    case 'R':
                        $response->results[$key]['action'] = 'Modificato in Riservato';
                        break;
                    case 'N':
                        $response->results[$key]['action'] = 'Modificato in NON Riservato';
                        break;
                    case 'F':
                        $response->results[$key]['action'] = 'Effettuata Forzatura';
                        break;
                    default:
                        $response->results[$key]['action'] = '-';
                        break;
                }

                $user = UserQuery::create()->findOneById($record->getUserId());
                if ($user !== null) {
                    $response->results[$key]['user_text'] = $user->getUsername();
                } else {
                    $response->results[$key]['user_text'] = '-';
                }
            }
        }

        return new JsonModel((array) $response);
    }

    public function protocolPermissions( $user, $data  )
    {

        $docUtils = new \Protocol\Model\ProtocolUtils($this->getServiceLocator()->get('Config'));
        return $docUtils->protocolPermissions($user,$data);
        /*
        $result = true;
        if ( !is_object( $user )){
            $result = false;
        }


        if ( ! $user->hasPermission(401)){
            $result = false;
        }


        if ( $data['reserved'] == 't' && ! $user->hasPermission(470)) {
            $result = false;
        }


        return $result;
        */
    }

    /**
     * function regenerateProtocolAction()
     *
     * @return json
     */
    public function regenerateProtocolAction()
    {
        $protocol_id = $this->params()->fromRoute('id');
        $response = new Response;

        if ( $this->session_auth > 0 ) {
            $user = UserQuery::create()->findOneById($this->session_auth['uid']);
            if ( ! $this->protocolPermissions($user, $data) ){
                $response->setStatus(403);
                $response->setSuccess(false);
                $response->message = "Non hai i permessi necessari per modificare protocolli. "
                        . "Utilizzare un utente abilitato oppure abilitare il permesso di modifica nella sezione protocolli all'interno dei gruppi.";
                return new JsonModel((array) $response);
            } else {
                $docUtils = new Protocol\Model\ProtocolUtils();
                $result = $docUtils->regenerateProtocol($protocol_id);
                $response->setStatus($result['status']);
                $response->setSuccess($result['success']);
                $response->message = $result['message'];
            }
        }

        return new JsonModel((array) $response);
    }

    # Function return distinct of mechan code of prtocols
    public function getMechanCodeAction() {
        $response = new Response;
        if ($this->session_auth['uid'] > 0) {
            $protocols = ProtocolQuery::create()->select(['mechan_code'])->filterByMechanCode(null, \Criteria::NOT_EQUAL)->distinct()->find()->toArray(null, false, BasePeer::TYPE_FIELDNAME);
            $response->results = [];
            foreach ($protocols as $key => $value) {
                $response->results[] = [
                    'mechan_code' => $value
                ];
            }
            $response->setSuccess(true);
            
        }

        return new JsonModel((array) $response);
    }

}
