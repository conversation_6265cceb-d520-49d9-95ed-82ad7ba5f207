<?php

/**
 * Zend Framework (http://framework.zend.com/)
 *
 * @link      http://github.com/zendframework/ZendSkeletonApplication for the canonical source repository
 * @copyright Copyright (c) 2005-2014 Zend Technologies USA Inc. (http://www.zend.com)
 * @license   http://framework.zend.com/license/new-bsd New BSD License
 */
return array(
    'controllers'     => array(
        'invokables' => array(
            'Protocol\Action'        => 'Protocol\Controller\ActionController',
            'Protocol\Correspondent' => 'Protocol\Controller\CorrespondentController',
            'Protocol\Linked'        => 'Protocol\Controller\LinkedController',
            'Protocol\Protocol'      => 'Protocol\Controller\ProtocolController',
            'Protocol\SendMethod'    => 'Protocol\Controller\SendMethodController',
            'Protocol\SubjectKind'   => 'Protocol\Controller\SubjectKindController',
            'Protocol\Type'          => 'Protocol\Controller\TypeController',
            'Protocol\Register'      => 'Protocol\Controller\RegisterController',
            'Protocol\Console'       => 'Protocol\Controller\ConsoleController',
            'Archive\Mail'           => 'Archive\Controller\MailController',
        )
    ),
    'router'          => array(
        'routes' => array(
            'protocol' => array(
                'type'          => 'Literal',
                'options'       => array(
                    'route' => '/protocol'
                ),
                'may_terminate' => false,
                'child_routes'  => array(
                    'action'               => array(
                        'type'    => 'Segment',
                        'options' => array(
                            'route'       => '/action[/:id]',
                            'constraints' => array(
                                'id' => '\d+'
                            ),
                            'defaults'    => array(
                                'controller' => 'Protocol\Action'
                            )
                        )
                    ),
                    'protocol-mechan-code' => [
                        'type'    => 'Segment',
                        'options' => [
                            'route'    => '/protocol/mechan-code',
                            'defaults' => [
                                'controller' => 'Protocol\Protocol',
                                'action'     => 'getMechanCode'
                            ]
                        ]
                    ],
                    'correspondent'        => array(
                        'type'    => 'Segment',
                        'options' => array(
                            'route'       => '/correspondent[/:id]',
                            'constraints' => array(
                                'id' => '\d+'
                            ),
                            'defaults'    => array(
                                'controller' => 'Protocol\Correspondent'
                            )
                        )
                    ),
                    'correspondent_link'   => array(
                        'type'    => 'Segment',
                        'options' => array(
                            'route'    => '/correspondent/bind',
                            'defaults' => array(
                                'controller' => 'Protocol\Correspondent',
                                'action'     => 'bind'
                            )
                        )
                    ),
                    'correspondent_origin' => array(
                        'type'    => 'Segment',
                        'options' => array(
                            'route'    => '/correspondent/origin',
                            'defaults' => array(
                                'controller' => 'Protocol\Correspondent',
                                'action'     => 'origin'
                            )
                        )
                    ),
                    'protocol'             => array(
                        'type'    => 'Segment',
                        'options' => array(
                            'route'       => '/protocol[/:id]',
                            'constraints' => array(
                                'id' => '\d+'
                            ),
                            'defaults'    => array(
                                'controller' => 'Protocol\Protocol'
                            )
                        )
                    ),
                    'protocol_link'        => array(
                        'type'    => 'Segment',
                        'options' => array(
                            'route'       => '/protocol/:id/link-protocols',
                            'defaults'    => array(
                                'controller' => 'Protocol\Protocol',
                                'action'     => 'linkProtocols'
                            )
                        )
                    ),
                    'protocol_regenerate'  => [
                        'type'     => 'Segment',
                        'options'  => [
                            'route'         => '/protocol/regenerate[/:id]',
                            'constraints'   => [
                                'id' => '\d+'
                            ],
                            'defaults'      => [
                                'controller' => 'Protocol\Protocol',
                                'action'     => 'regenerateProtocol'
                            ]
                        ]
                    ],
                    'history'              => array(
                        'type'    => 'Segment',
                        'options' => array(
                            'route'    => '/history',
                            'defaults' => array(
                                'controller' => 'Protocol\Protocol',
                                'action'     => 'history'
                            )
                        )
                    ),
                    'linked'               => array(
                        'type'    => 'Segment',
                        'options' => array(
                            'route'       => '/linked[/:id]',
                            'constraints' => array(
                                'id' => '\d+'
                            ),
                            'defaults'    => array(
                                'controller' => 'Protocol\Linked'
                            )
                        )
                    ),
                    'sendmethod'           => array(
                        'type'    => 'Segment',
                        'options' => array(
                            'route'       => '/sendmethod[/:id]',
                            'constraints' => array(
                                'id' => '\d+'
                            ),
                            'defaults'    => array(
                                'controller' => 'Protocol\SendMethod'
                            )
                        )
                    ),
                    'subjectkind'          => array(
                        'type'    => 'Segment',
                        'options' => array(
                            'route'       => '/subjectkind[/:id]',
                            'constraints' => array(
                                'id' => '\d+'
                            ),
                            'defaults'    => array(
                                'controller' => 'Protocol\SubjectKind'
                            )
                        )
                    ),
                    'type'                 => array(
                        'type'    => 'Segment',
                        'options' => array(
                            'route'       => '/type[/:id]',
                            'constraints' => array(
                                'id' => '\d+'
                            ),
                            'defaults'    => array(
                                'controller' => 'Protocol\Type'
                            )
                        )
                    ),
                    'typetree'             => array(
                        'type'    => 'Segment',
                        'options' => array(
                            'route'    => '/type/flat',
                            'defaults' => array(
                                'controller' => 'Protocol\Type',
                                'action'     => 'flat'
                            )
                        )
                    ),
                    'register'             => array(
                        'type'    => 'Segment',
                        'options' => array(
                            'route'    => '/register[/:id][/:action]',
                            'defaults' => array(
                                'controller' => 'Protocol\Register'
                            ),
                            'constraints' => array(
                                'id' => '\d+'
                            ),
                        )
                    ),
                    'mail-protocol-webhook'   => array(
                        'type'    => 'Segment',
                        'options' => array(
                            'route'    => '/mail/webhook',
                            'defaults' => array(
                                'controller' => 'Archive\Mail',
                                'action'    => 'mailProtocolWebhook'
                            ),
                        )
                    )
                )
            )
        )
    ),
    'view_manager'    => array(
        'strategies' => array(
            'ViewJsonStrategy'
        )
    ),
    'service_manager' => array(
        'abstract_factories' => array(
            'Zend\Cache\Service\StorageCacheAbstractServiceFactory',
            'Zend\Log\LoggerAbstractServiceFactory'
        ),
        'aliases'            => array(
            'translator' => 'MvcTranslator'
        )
    ),
    'translator'      => array(
        'locale'                    => 'it_IT',
        'translation_file_patterns' => array(
            array(
                'type'     => 'gettext',
                'base_dir' => __DIR__ . '/../language',
                'pattern'  => '%s.mo'
            )
        )
    ),
    'console'         => array(
        'router' => array(
            'routes' => array(
                'regenerate-protocol' => array(
                    'options' => array(
                        'route'    => 'regenerate-protocol <protocol_id>',
                        'defaults' => array(
                            'controller' => 'Protocol\Console',
                            'action'     => 'regenerateProtocol'
                        )
                    )
                ),
                'export-protocols' => array(
                    'options' => array(
                        'route'    => 'export-protocols [<protocol_id>]',
                        'defaults' => array(
                            'controller' => 'Protocol\Console',
                            'action'     => 'exportProtocols'
                        )
                    )
                ),
                'setup-titolario-piemonte' => array(
                    'options' => array(
                        'route'    => 'setup-titolario-piemonte <mode>',
                        'defaults' => array(
                            'controller' => 'Protocol\Console',
                            'action'     => 'setupTitolarioPiemonte'
                        )
                    )
                ),
                'setup-titolario-due-livelli' => array(
                    'options' => array(
                        'route'    => 'setup-titolario-due-livelli',
                        'defaults' => array(
                            'controller' => 'Protocol\Console',
                            'action'     => 'setupTitolarioDueLivelli'
                        )
                    )
                ),
                'setup-titolario' => array(
                    'options' => array(
                        'route'    => 'setup-titolario <mode>',
                        'defaults' => array(
                            'controller' => 'Protocol\Console',
                            'action'     => 'setupTitolario'
                        )
                    )
                )
            )
        )
    )
);
