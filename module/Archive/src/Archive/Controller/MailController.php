<?php

namespace Archive\Controller;

use ArchiveMailAttachmentQuery;
use ArchiveMailQuery;
use ArchiveMailAccountQuery;
use Database\Archive\ArchiveMailDocumentQuery;
use Database\Protocol\ProtocolQuery;
use MT\Logger\LoggerZend,
    MT\Sencha\Response,
    Core\Model\Session\Session,
    Core\Model\Log,
    Zend\Mvc\Controller\AbstractRestfulController,
    Zend\View\Model\JsonModel,
    Webklex\PHPIMAP\ClientManager,
    Database\Archive,
    Database\Core,
    Database\Protocol,
    Core\Model\Mailer,
    Zend\Mail\Transport\Smtp as SmtpTransport,
    Zend\Mail\Transport\SmtpOptions,
    Core\Model\Mc2BaseAuth;

class MailController extends AbstractRestfulController
{

    private $fromLocal;
    protected $logger;
    protected $session_auth;

    public function __construct()
    {
        $this->logger = new LoggerZend;
        $this->session_auth = Session::readSessionAuth();

        $this->fromLocal = false;
        if (isset($_SERVER['REMOTE_ADDR']) && ($_SERVER['REMOTE_ADDR'] == '127.0.0.1' || $_SERVER['REMOTE_ADDR'] == '::1')) {
            $this->fromLocal = true;
        }
    }

    public function getList()
    {
        $response = new Response;

        if ($this->session_auth['uid'] > 0) {

            $filter = $this->params()->fromQuery();
            if (! isset($filter['query'])) {
                $filter['query'] = "";
            }
            $results = Archive\ArchiveMailQuery::create()->getList(false, $filter);
            $response->total = Archive\ArchiveMailQuery::create()->getList(true, $filter);

            foreach ($results as $k => $res) {
                $results[$k]['message_text'] = strip_tags(html_entity_decode($res['message']));
                $results[$k]['date'] = date('c', strtotime($res['date']));
            }

            $response->setSuccess(true);
            $response->results = array_values($results);
        }

        return new JsonModel((array) $response);
    }

    public function get($id)
    {
        $response = new Response;

        if ($this->session_auth['uid'] > 0) {
            $mail = Archive\ArchiveMailQuery::create()->findOneById($id)->toArray(\BasePeer::TYPE_FIELDNAME);
            $mail['attachments'] = Archive\ArchiveMailAttachmentQuery::create()
                ->filterByMail($mail['id'])
                ->find()->toArray(null, false, \BasePeer::TYPE_FIELDNAME);
            unset($mail['raw']);
            $from = $mail['from'];
            # $from contains the email address and the name, the email address is between () and ) or <>
            if ( preg_match('/\((.*?)\)/', $from, $matches) || preg_match('/<(.*?)>/', $from, $matches)) {
                $mail['from_email'] = $matches[1];
                $mail['from_name'] = str_replace($matches[0], '', $from) ?? '';
            } else {
                $mail['from_email'] = $from;
                $mail['from_name'] = '';
            }


            $response->setSuccess(true);
            $response->results = $mail;
        }

        return new JsonModel((array) $response);
    }

    public function update($id, $data)
    {
        $response = new Response;

        if ($this->session_auth['uid'] > 0) {

            $mail = Archive\ArchiveMailQuery::create()->findOneById($id);
            $mail->setDeleted(array_key_exists('deleted', $data) ? $data['deleted'] : $mail->getDeleted());

            if (array_key_exists('assign_to_user', $data) || array_key_exists('assign_to_office', $data)) {
                $mail->setAssignToUser(null);
                $mail->setAssignToOffice(null);
            }
            $mail->setAssignToUser(array_key_exists('assign_to_user', $data) ? $data['assign_to_user'] : $mail->getAssignToUser());
            $mail->setAssignToOffice(array_key_exists('assign_to_office', $data) ? $data['assign_to_office'] : $mail->getAssignToOffice());
            $mail->setAssignFromUser(array_key_exists('assign_from_user', $data) ? $data['assign_from_user'] : $mail->getAssignFromUser());

            if ($mail->save()) {
                $response->setSuccess(true);
            }
            $response->results = $mail->toArray(\BasePeer::TYPE_FIELDNAME);
        }

        return new JsonModel((array) $response);
    }

    public function countAction()
    {
        $response = new Response;
        if ($this->session_auth['uid'] > 0) {
            $qfilter = $this->params()->fromPost();

            $filter = [
                'deleted' => false,
                'active' => true,
                'out' => false,
                'is_sent' => isset($qfilter['is_sent']) ? $qfilter['is_sent'] : false
            ];

            $response->results = Archive\ArchiveMailQuery::create()->getList(true, $filter);
            $response->setSuccess(true);
        }

        return new JsonModel((array) $response);
    }

    public function create($data)
    {

        $response = new Response;

        if ($this->session_auth['uid']) {


            $isNewMail = empty($data['mail_id']) && empty($data['document']) && empty($data['protocol']);
            $mailer = new Mailer();
            $config = $this->getServiceLocator()->get('Config');
            // Mail inviata da Archivio
            $attachments = [];
            $mailId = md5(uniqid());
            $webhook = null;
            if ($data['document']) {
                $messageId = $mailId;
                $webhook = $mailer->getSchoolUrl() . "/mc2-api/archive/mail/webhook";
                $files = Archive\ArchiveDocumentFileQuery::create()->filterByArchiveDocument($data['document'])->find();
            } else if ($data['protocol']) { // Mail inviata da Protocollo
                $messageId = $data['protocol'];
                $webhook = $mailer->getSchoolUrl() . "/mc2-api/protocol/mail/webhook";
                $filesProtocol = Protocol\ProtocolDocumentQuery::create()->filterByProtocolId($data['protocol'])->select(['document_id'])->find();
                $files = Archive\ArchiveDocumentFileQuery::create()->filterById(array_values($filesProtocol->toArray()))->find();
            } else if ($data['mail_id']) {
                $messageId = $data['mail_id'];
                // $webhook = $mailer->getSchoolUrl() . "/mc2-api/archive/mail/webhook";
                $files = Archive\ArchiveMailAttachmentQuery::create()->filterByMail($data['mail_id'])->find();
            } else if ($isNewMail) {
                // New mail sending
                $messageId = $mailId;
                $files = [];
            }
            $baseFilePath = trim($data['mail_id'] ? $config['PATHS']['ARCHIVE']['MAIL_ATTACHMENTS'] : $config['PATHS']['ARCHIVE']['DOCUMENTS']);
            foreach ($files as $file) {

                // if $file->getPath() contains $baseFilePath, remove it so concatenation does not create a false path
                $filePath = $file->getPath();
                if (strpos($filePath, $baseFilePath) === 0) {
                    $filePath = substr($filePath, strlen($baseFilePath));
                    $filePath = ltrim($filePath, '/');
                }
                $fpath = str_replace('//', '/', $baseFilePath . '/' . $filePath);
                
                // if $fpath does not exist, skip the attachment
                if (!file_exists($fpath) || !file_get_contents($fpath)) {
                    continue;
                }
                $attachments[] = [
                    'filename' => $data['mail_id'] ? $file->getName() : $file->getFilename(),
                    // 'content'  => base64_encode(file_get_contents($baseFilePath . '/' . $file->getPath())),
                    // $file->getPath() contains the full path to the file
                    'content'  => base64_encode(file_get_contents($fpath)),
                    'mimetype' => $file->getFiletype()
                ];

                /*if (getenv('MC2DEBUG')) {
                    foreach ($attachments as $key => $attachment) {q
                        $attachments[$key]['content'] = base64_encode("TEST MESSAGE ATTACHMENT {$key}");
                        $attachments[$key]['mimetype'] = 'text/plain';
                        $attachments[$key]['filename'] = "test{$key}.txt";
                    }
                }*/
            }

            $data['to'] = json_decode($data['to'], true);
            $to = [];
            foreach ($data['to'] as $value) {
                $value['email'] = trim($value['email']);

                $id = (int) str_replace($value['type'], '', $value['id']);

                if ($value['type'] == 'G') {

                    $emails = Core\CoreContactsQuery::create()->getMailsFromGroup($id);
                    foreach ($emails as $email) {
                        $email = trim($email);
                        if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
                            $to[] = $email;
                        } else {
                            $response->message = 'Errore email destinatario';
                            $response->setSuccess(false);
                            return new JsonModel((array) $response);
                        }
                    }
                } else {
                    if (filter_var($value['email'], FILTER_VALIDATE_EMAIL)) {
                        $to[] = $value['email'];
                    } else {
                        $response->message = 'Errore email destinatario';
                        $response->setSuccess(false);
                        return new JsonModel((array) $response);
                    }
                }
            }

            $message = [];
            $message['id'] = $messageId;
            $message['subject'] = $data['subject'];
            $message['content_subtype'] = "html";
            $message['tag'] = 'mc2_mail';

            if ($mailer->isPrivate() and !empty($data['account_id'])) {
                $message['account']  = $data['account_id'];
            }

            if (getenv('MC2DEBUG')) {
                $message['to'] = ['<EMAIL>'];
            } else {
                $message['to'] = $to;
            }
            $message['body'] = $data['message'];

            if ($webhook) {
                $message['webhook'] = $webhook;
            }
            $message['attachments'] = $attachments;

            try {
                $mailer->send([$message]);
                $response->setSuccess(true);
            } catch (\Exception $e) {
                $response->message = 'Errore durante l\'invio della mail.';
                Log::error(Log::SECTION_CCP, "Error senting mail to Mailer: " . $e->getMessage());
            }

            // PER ORA NON SI TIENE TRACCIA DELLA MAIL INVIATA
            // FARA' PARTE DELLA VISUALIZZAZIONE DELLA MAIL IN USCITA FATTA DAL MAILER
            if ($data['mail_id'] or $isNewMail) {
                return new JsonModel((array) $response);
            }


            // Create out mail
            $mailerAccountId = Archive\ArchiveMailAccountQuery::create()->filterByOut(true)->filterByActive(true)->findOne();
            $archiveMail = new Archive\ArchiveMail();
            $archiveMail->setId($mailId);
            $archiveMail->setAccount($mailerAccountId->getId());
            $archiveMail->setDate(date('c'));
            $archiveMail->setMessage($data['message']);
            $archiveMail->setFrom(!empty($data['email_from']) ? $data['email_from'] : ' - ');
            $archiveMail->setTo(implode(',', $message['to']));
            $archiveMail->setSubject($data['subject']);

            if ($archiveMail->save()) {

                if ($data['document']) {
                    // Link mail with document flow
                    $adf = new Archive\ArchiveMailDocument();
                    $adf->setMail($mailId);
                    $adf->setAccount($mailerAccountId->getId());
                    $adf->setDocument($data['document']);
                    $adf->save();
                } else {
                    $protocol = ProtocolQuery::create()->findPk($data['protocol']);
                    $protocol->setMailSending(date('c'));
                    $protocol->save();
                }
            }
            
            // if account is not outlook or gmail, create a copy of the message and append it to the sent folder
            // first of all, retrieve the account corresponding to $data['email_from']
            $account = Archive\ArchiveMailAccountQuery::create()->filterByName($data['email_from'])->findOne();
            if ($account && $account->getAuthentication() != 'oauth') {
                // it's an IMAP account, create a copy of the message and append it to the sent folder
                $imap_copy = $this->appendToSentFolder($account, $message);                
            }
        }

        return new JsonModel((array) $response);
    }

    private function appendToSentFolder($account, $message) {
        $mailer = new Mailer();

        $c = $mailer->getCredentials($account->getName());
        if (isset($c['access_token'])) {
            return false;
        }
        $password = $c['password'];
        $username = $c['username'];
        $host = $account->getHost();
        $port = $account->getPort();
        $protocol = Archive\ArchiveMailProtocolQuery::create()->findOneById($account->getProtocol())->getName();
        $security = Archive\ArchiveMailSecurityQuery::create()->findOneById($account->getSecurity())->getName();
        $imapEncoding = $account->getImapEncoding();

        $cm = new ClientManager();
        $validate_cert = true;
        if ($security == 'ssl/novalidate-cert') {
            $validate_cert = false;
            $security = 'ssl';
        }
        $client = $cm->make([
            'host' => $host,
            'port' => $port,
            'encryption' => $security, // 'tls',
            'validate_cert' => $validate_cert,
            'username' => $username,
            'password' => $password,
            'protocol' => $protocol,
            'authentication' => 'login'
        ]);
        $client->connect();
        $sentFolder = $account->getSentFolder();

        $boundary = uniqid('part');
        $message['from'] = $account->getName();
        // Create Attachment string

        $attachments = '';
        foreach ($message['attachments'] as $attachment) {
            $attachments .= '--' . $boundary . PHP_EOL;
            $attachments .= 'Content-Type: ' . $attachment['mimetype'] . PHP_EOL;
            $attachments .= 'Content-Transfer-Encoding: base64' . PHP_EOL;
            $attachments .= 'Content-Disposition: attachment; filename="' . $attachment['filename'] . '"' . PHP_EOL;
            $attachments .= PHP_EOL . $attachment['content'] . PHP_EOL;          

        }

        $to = is_array($message['to']) ? implode(', ', $message['to']) : $message['to'];


        $rawMessage = '';
        $rawMessage .= "From: {$message['from']}" . PHP_EOL;
        $rawMessage .= "To: {$to}" . PHP_EOL;
        $rawMessage .= "Subject: {$message['subject']}" . PHP_EOL;
        $rawMessage .= "Date: {$message['date']}" . PHP_EOL;
        $rawMessage .= "MIME-Version: 1.0" . PHP_EOL;
        $rawMessage .= "Content-Type: multipart/mixed; boundary={$boundary}" . PHP_EOL;
        $rawMessage .= PHP_EOL;
        $rawMessage .= "--{$boundary}" . PHP_EOL;
        $rawMessage .= "Content-Type: text/html; charset=utf-8" . PHP_EOL;
        $rawMessage .= "Content-Transfer-Encoding: 8bit" . PHP_EOL;
        $rawMessage .= PHP_EOL;
        $rawMessage .= $message['body'] . PHP_EOL;
        $rawMessage .= $attachments ? $attachments . PHP_EOL: '';
        $rawMessage .= "--{$boundary}--" . PHP_EOL;
        $folder = $client->getFolderByPath($sentFolder);
        return $folder->appendMessage($rawMessage);
        
    }

    function customMailAction()
    {
        $response = new Response;
        $request = $this->getRequest();
        $data = json_decode($request->getContent(), true);
        if ($this->session_auth['uid'] > 0 || $this->fromLocal || Mc2BaseAuth::auth($this->getRequest())) {
            $to = $data['to'];
            $subject = $data['subject'];
            $text = $data['text'];
            $attachments = $data['attachments'] ? $data['attachments'] : [];
            $mailAccount = Archive\ArchiveMailAccountQuery::create()->findOneById($data['mail_account']);

            $errors = [];
            if (!$mailAccount) {
                $errors[] = 'Selezionare un account di posta valido';
            }
            if (!$to or !filter_var($to, FILTER_VALIDATE_EMAIL)) {
                $errors[] = 'Inserire una mail di destinazione valida';
            }
            if (!$subject) {
                $errors[] = 'Inserire un oggetto';
            }

            if ($errors) {
                $response->setSuccess(false);
                $response->message = join('. ', $errors);
                return new JsonModel((array) $response);
            }

            $text = new \Zend\Mime\Part($text);
            $text->type = \Zend\Mime\Mime::TYPE_HTML;
            $text->charset = 'utf-8';
            $parts = [$text];

            # In caso ci siano allegati
            foreach ($attachments as $file) {
                if (!file_exists($file)) {
                    $response->message = "L'allegato '{$file}' non esiste";
                    $response->setSuccess(false);
                    return new JsonModel((array) $response);
                }

                $fileContent = fopen($file, 'r');
                $attachment = new \Zend\Mime\Part($fileContent);
                $attachment->encoding = \Zend\Mime\Mime::ENCODING_BASE64;
                $attachment->type = mime_content_type($file);
                $attachment->filename = basename($file);
                $attachment->disposition = \Zend\Mime\Mime::DISPOSITION_ATTACHMENT;
                $parts[] =  $attachment;
            }

            $mimeMessage = new \Zend\Mime\Message();
            $mimeMessage->setParts($parts);

            $outname = Archive\ArchiveMailAccountQuery::create()->getSender($mailAccount);

            $connection_class = $mailAccount->getConnectionClass();
            $connection_class = $connection_class != '' ? $connection_class : 'plain';

            // Security
            $security = (
                Archive\ArchiveMailSecurityQuery::create()
                ->findOneById($mailAccount->getSecurity())
                ->getName()
            );

            if ($connection_class == 'plain-tls') {
                $connection_class = 'plain';
                $security = 'tls';
            }

            $message = new \Zend\Mail\Message();
            $message->setBody($mimeMessage);
            $message->setFrom($outname);
            $message->setTo($to);
            $message->setSubject($subject);

            $transport = new SmtpTransport();

            $options = new SmtpOptions(
                array(
                    'host'              => $mailAccount->getHost(),
                    'connection_class'  => $connection_class,
                    'port'              => $mailAccount->getPort(),
                    'connection_config' => array(
                        'username' => $mailAccount->getUsername(),
                        'password' => base64_decode($mailAccount->getPassword()),
                        'ssl'      => $security
                    ),
                )
            );

            $transport->setOptions($options);
            try {
                $transport->send($message);
            } catch (\Exception $e) {

                $this->logger->log(
                    array(
                        'Type'    => 'ERROR',
                        'Scope'   => __FILE__,
                        'Event'   => basename(__FILE__, ".php"),
                        'Message' => "Errore invio mail custom",
                        'Context' => $e->getMessage() . ' - ' . $e->getTraceAsString()
                    )
                );

                $response->message = 'Errore durante l\'invio della mail, verificare che sia presente la connessione e che i dati dell\'account di posta siano stati inseriti correttamente';
                $response->detail = $e->getMessage();
                $response->setSuccess(false);
                return new JsonModel((array) $response);
            }
            $response->setSuccess(true);
        } else {
            $response->message = "Sessione scaduta";
            $response->setSuccess(false);
            return new JsonModel((array) $response);
        }



        return new JsonModel((array) $response);
    }

    public function mailDocumentWebhookAction()
    {
        $data = $this->params()->fromPost();

        $id = $data['id'];
        $status = $data['state'];
        if ($id && $status) {
            $mailDoc = ArchiveMailDocumentQuery::create()->findOneByMail($id);
            if ($mailDoc->getSent()) {
                return new JsonModel((array) []);
            }

            if (!$mailDoc) {
                Log::error(Log::SECTION_CCP, "Mail webhook (Mailer) with no corresponding id in archive_mail_document table: " . $id);
            } else {
                switch ($status) {
                    case 'sent':
                        $mailDoc->setSent(date('c'));
                        $mailDoc->save();
                        break;
                    case 'in-progress':
                        break;
                    default:
                        Log::error(Log::SECTION_CCP, "Unknown status or failure: " . json_encode($data));
                }
            }
        }
        return new JsonModel((array) []);
    }

    public function mailProtocolWebhookAction()
    {
        $data = $this->params()->fromPost();
        $id = $data['id'];
        $status = $data['state'];

        if ($id && $status) {
            $protcol = ProtocolQuery::create()->findPk($id);
            if (!$protcol) {
                Log::error(Log::SECTION_CCP, "Mail webhook (Mailer) with no corresponding id in protocol_protocol table: " . $id);
            } else {
                switch ($status) {
                    case 'sent':
                        $protcol->setMailSending(NULL);
                        $protcol->save();
                        break;
                    case 'in-progress':
                        break;
                    default:
                        Log::error(Log::SECTION_CCP, "Unknown status or failure: " . json_encode($data));
                }
            }
        }
        return new JsonModel((array) []);
    }
}
