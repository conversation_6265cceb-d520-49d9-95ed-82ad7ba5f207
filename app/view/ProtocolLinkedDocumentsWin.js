/*
 * File: app/view/ProtocolLinkedDocumentsWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ProtocolLinkedDocumentsWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ProtocolLinkedDocumentsWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.column.Action',
        'Ext.grid.View'
    ],

    permissible: true,
    height: 250,
    id: 'ProtocolLinkedDocumentsWin',
    itemId: 'ProtocolLinkedDocumentsWin',
    width: 600,
    resizable: false,
    title: 'Documenti allegati',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    flex: 1,
                    border: false,
                    id: 'ProtocolLinkedDocumentsGrid',
                    itemId: 'ProtocolLinkedDocumentsGrid',
                    emptyText: 'Nessun documento allegato.',
                    enableColumnHide: false,
                    enableColumnMove: false,
                    enableColumnResize: false,
                    sortableColumns: false,
                    store: 'ProtocolLinkedDocuments',
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            draggable: false,
                            resizable: false,
                            dataIndex: 'filename',
                            hideable: false,
                            text: 'Nome',
                            flex: 1
                        },
                        {
                            xtype: 'actioncolumn',
                            draggable: false,
                            id: 'ProtocolLinkedDocumentsDownloadOriginalColumn',
                            itemId: 'ProtocolLinkedDocumentsDownloadOriginalColumn',
                            width: 120,
                            resizable: false,
                            hideable: false,
                            text: 'Originali',
                            align: 'center',
                            items: [
                                {
                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                        window.open("/mc2-api/archive/document_file/" + record.get('id'), "_blank");
                                    },
                                    iconCls: 'icon-arrow_down',
                                    tooltip: 'Scarica',
                                    
                                },
                            ]
                        },
                        {
                            xtype: 'actioncolumn',
                            draggable: false,
                            id: 'ProtocolLinkedDocumentsDownloadColumn',
                            itemId: 'ProtocolLinkedDocumentsDownloadColumn',
                            width: 120,
                            resizable: false,
                            hideable: false,
                            text: 'Con stampigliatura',
                            align: 'center',
                            items: [
                                {
                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                        if (record.get('filetype') == 'application/pdf') {
                                            window.open("/mc2-api/archive/document_file/" + record.get('id') + "?header=1", "_blank");   
                                        }
                                    },
                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                        if (r.get('filetype') !== 'application/pdf') {
                                            return 'icon-information';
                                        }
                                        return 'icon-layout_header';
                                    },
                                    getTip: function(v, metadata, r, rowIndex, colIndex, store) {
                                        if (r.get('filetype') == 'application/pdf') {
                                            return 'Scarica';
                                        }
                                        return 'Al documento non può essere applicata la stampigliatura perché in formato non compatibile.';
                                    }
                                }
                            ],
                            
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});