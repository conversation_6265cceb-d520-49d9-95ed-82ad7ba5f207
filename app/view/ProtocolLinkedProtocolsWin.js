/*
 * File: app/view/ProtocolLinkedProtocolsWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ProtocolLinkedProtocolsWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ProtocolLinkedProtocolsWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.column.Date',
        'Ext.grid.column.Action',
        'Ext.grid.View'
    ],

    height: 315,
    id: 'ProtocolLinkedProtocolsWin',
    itemId: 'ProtocolLinkedProtocolsWin',
    width: 565,
    resizable: false,
    title: 'Protocolli collegati',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    flex: 1,
                    border: false,
                    id: 'ProtocolLinkedProtocolsGrid',
                    itemId: 'ProtocolLinkedProtocolsGrid',
                    emptyText: 'Nessun protocollo collegato.',
                    enableColumnHide: false,
                    enableColumnMove: false,
                    enableColumnResize: false,
                    sortableColumns: false,
                    store: 'ProtocolLinkedProtocolsForm',
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            draggable: false,
                            width: 75,
                            resizable: false,
                            align: 'right',
                            dataIndex: 'protocol_number',
                            hideable: false,
                            text: 'Numero'
                        },
                        {
                            xtype: 'datecolumn',
                            draggable: false,
                            resizable: false,
                            align: 'center',
                            dataIndex: 'date',
                            hideable: false,
                            text: 'Data',
                            format: 'd/m/Y'
                        },
                        {
                            xtype: 'gridcolumn',
                            draggable: false,
                            resizable: false,
                            dataIndex: 'type_text',
                            hideable: false,
                            text: 'Titolario'
                        },
                        {
                            xtype: 'gridcolumn',
                            draggable: false,
                            resizable: false,
                            dataIndex: 'description',
                            hideable: false,
                            text: 'Oggetto',
                            flex: 1
                        },
                        {
                            xtype: 'actioncolumn',
                            draggable: false,
                            width: 20,
                            resizable: false,
                            hideable: false,
                            items: [
                                {
                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                        var msg = '';

                                        if (!record.get('reserved')) {
                                            var canceled = record.get('canceled') ? 'Sì' : 'No',
                                                date = record.get('date');

                                            msg = '<div style="display: table">' +
                                            '<div style="display: table-row">' +
                                            '<div style="display: table-cell"><b>Numero:&nbsp;</b></div>' +
                                            '<div style="display: table-cell">' + record.get('protocol_number') + '</div>' +
                                            '</div>' +
                                            '<div style="display: table-row">' +
                                            '<div style="display: table-cell"><b>Data:&nbsp;</b></div>' +
                                            '<div style="display: table-cell">' + date.getDate() + '/' + (date.getMonth() + 1) + '/' + date.getFullYear() + '</div>' +
                                            '</div>' +
                                            '<div style="display: table-row">' +
                                            '<div style="display: table-cell"><b>Tipo:&nbsp;</b></div>' +
                                            '<div style="display: table-cell">' + record.get('direction_text') + '</div>' +
                                            '</div>' +
                                            '<div style="display: table-row">' +
                                            '<div style="display: table-cell"><b>Oggetto:&nbsp;</b></div>' +
                                            '<div style="display: table-cell">' + record.get('description') + '</div>' +
                                            '</div>' +
                                            '<div style="display: table-row">' +
                                            '<div style="display: table-cell"><b>Titolario:&nbsp;</b></div>' +
                                            '<div style="display: table-cell">' + record.get('type_text') + '</div>' +
                                            '</div>' +
                                            '<div style="display: table-row">' +
                                            '<div style="display: table-cell"><b>Corrispondenti:&nbsp;</b></div>' +
                                            '<div style="display: table-cell">' + record.get('correspondents_text') + '</div>' +
                                            '</div>' +
                                            '<div style="display: table-row">' +
                                            '<div style="display: table-cell"><b>Annullato:&nbsp;</b></div>' +
                                            '<div style="display: table-cell">' + canceled + '</div>' +
                                            '</div>' +
                                            '<div style="display: table-row">' +
                                            '<div style="display: table-cell"><b>Documenti allegati:&nbsp;</b></div>' +
                                            '<div style="display: table-cell">' + record.get('linked_documents').length + '</div>' +
                                            '</div>' +
                                            '<div style="display: table-row">' +
                                            '<div style="display: table-cell"><b>Atto:&nbsp;</b></div>' +
                                            '<div style="display: table-cell">' + record.get('external_act_number') + '</div>' +
                                            '</div>' +
                                            '<div style="display: table-row">' +
                                            '<div style="display: table-cell"><b>Fascicolo:&nbsp;</b></div>' +
                                            '<div style="display: table-cell">' + record.get('dossier') + '</div>' +
                                            '</div>' +
                                            '</div>';
                                        } else {
                                            msg = 'Protocollo Riservato';
                                        }
                                        Ext.Msg.alert('Dettagli protocollo n. ' + record.get('protocol_number'), msg);
                                    },
                                    iconCls: 'icon-magnifier',
                                    tooltip: 'Dettagli'
                                }
                            ]
                        }
                    ]
                }
            ],
            dockedItems: [
            {
                xtype: 'toolbar',
                dock: 'top',
                items: [
                    {
                        xtype: 'button',
                        handler: function(button, e) {
                            var links = Ext.getStore('ProtocolLinkedProtocolsForm').getRange();

                            Ext.widget('ProtocolProtocolNewLinkedProtocolsPickerWin').show();
                            Ext.getStore('ProtocolProtocolsForm').load({
                                callback: function(records, operation, success) {
                                    if (success) {
                                        Ext.getCmp('ProtocolProtocolNewLinkedProtocolsPickerGrid').getSelectionModel().select(links);
                                    }
                                }
                            });
                        },
                        iconCls: 'icon-link',
                        text: 'Collega Protocolli',
                        id: 'ProtocolProtocolNewProtocolsLinkBtn'
                    },
                    // spacer
                    {
                        xtype: 'tbspacer',
                        flex: 1
                    },
                    // salva button
                    {
                        xtype: 'button',
                        handler: function(button, e) {
                            var links = Ext.getStore('ProtocolLinkedProtocolsForm').getRange();
                            var protocolId = Ext.getCmp('ProtocolLinkedProtocolsWin').record.get('id');
                            var data = [];
                            links.forEach(function(l) {
                                data.push(l.get('id'));
                            });
                            Ext.Ajax.request({
                                url: '/mc2-api/protocol/protocol/' + protocolId + '/link-protocols',
                                method: 'POST',
                                params: {
                                    protocols: Ext.encode(data)
                                },
                                success: function() {
                                    Ext.Msg.alert('Successo', 'Protocolli collegati');
                                    Ext.getStore('ProtocolProtocols').load();
                                }
                            }); 
                        },
                        iconCls: 'icon-disk',
                        text: 'Salva',
                    }
                ]
            }
        ],
        });

        me.callParent(arguments);
    }

});