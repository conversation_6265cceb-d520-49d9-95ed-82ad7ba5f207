
from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated

from .models import Institute
from .serializers import InstituteSerializer
from ..authentication.base import NextApiAuthentication


class InstituteView(viewsets.ModelViewSet):
    queryset = Institute.objects.all()
    serializer_class = InstituteSerializer
    authentication_classes = [NextApiAuthentication]
    permission_classes = [IsAuthenticated]
