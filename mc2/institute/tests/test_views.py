from unittest.mock import patch
from django.urls import reverse
from django.contrib.auth import get_user_model

from rest_framework.test import APITestCase
from rest_framework import status

from mc2.institute.tests.factories import InstituteFactory


class InstituteListViewTest(APITestCase):
    def setUp(self):
        self.url = reverse('institute-list')
        self.institute = InstituteFactory()

    @patch('mc2.mastercom.next_api.NextApi.verify', return_value={'type': {'mc2': 4}, 'usr': 'test', 'id': 'test'})
    def test_get_institute_list_with_token_authentication(self, mock_verify):
        response = self.client.get(self.url, HTTP_AUTHORIZATION='valid_token')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['name'], self.institute.name)

    @patch('mc2.mastercom.next_api.NextApi.verify', return_value=False)
    def test_get_institute_list_with_invalid_token(self, mock_verify):
        response = self.client.get(self.url, HTTP_AUTHORIZATION='invalid_token')
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)


class InstituteCreateViewTest(APITestCase):
    def setUp(self):
        self.url = reverse('institute-list')
        
    @patch('mc2.mastercom.next_api.NextApi.verify', return_value={'type': {'mc2': 4}, 'usr': 'test', 'id': 'test'})
    def test_create_institute_with_minimal_data(self, mock_verify):
        data = {
            'name': 'Test institute',
            'mechan_code': '1234567890'
        }
        response = self.client.post(self.url, data, HTTP_AUTHORIZATION='valid_token')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], data['name'])
        self.assertEqual(response.data['mechan_code'], data['mechan_code'])
        self.assertEqual(response.data['def_field'], False)

    @patch('mc2.mastercom.next_api.NextApi.verify', return_value={'type': {'mc2': 4}, 'usr': 'test', 'id': 'test'})
    def test_create_institute_with_invalid_data(self, mock_verify):
        invalid_data = {}
        response = self.client.post(self.url, invalid_data, HTTP_AUTHORIZATION='valid_token')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

class InstituteUpdateViewTest(APITestCase):
    def setUp(self):
        self.institute = InstituteFactory(name='Old name', mechan_code='OLDMECHAN')
        self.url = reverse('institute-detail', kwargs={'pk': self.institute.institute_id})
        
    @patch('mc2.mastercom.next_api.NextApi.verify', return_value={'type': {'mc2': 4}, 'usr': 'test', 'id': 'test'})
    def test_update_institute_with_valid_data(self, mock_verify):
        data = {
            'name': 'Updated institute',
            'mechan_code': 'UPDATEMECHAN'
        }
        response = self.client.put(self.url, data, HTTP_AUTHORIZATION='valid_token')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], data['name'])
        self.assertEqual(response.data['mechan_code'], data['mechan_code'])

    @patch('mc2.mastercom.next_api.NextApi.verify', return_value={'type': {'mc2': 4}, 'usr': 'test', 'id': 'test'})
    def test_update_institute_with_only_mechan_code(self, mock_verify):
        data = {
            'name': 'Updated institute',
            'mechan_code': 'UPDATEMECHAN'
        }
        response = self.client.put(self.url, data, HTTP_AUTHORIZATION='valid_token')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], data['name'])
        self.assertEqual(response.data['mechan_code'], data['mechan_code'])

    @patch('mc2.mastercom.next_api.NextApi.verify', return_value={'type': {'mc2': 4}, 'usr': 'test', 'id': 'test'})
    def test_update_institute_with_invalid_data(self, mock_verify):
        invalid_data = {}
        response = self.client.put(self.url, invalid_data, HTTP_AUTHORIZATION='valid_token')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

