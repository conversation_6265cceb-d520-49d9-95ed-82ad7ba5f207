[application]
name = mc2
version = 3.14.167


section = main
priority = optional
architecture = all
depends[] = "php5 (>=5.5.35) | php (>=1:7.0)"
depends[] = "php5-pgsql (>=5.5.35) | php-pgsql (>=1:7.0)"
depends[] = "master-disaster (>=1.0.63)"
depends[] = "master-apache (>=3.0.16)"
depends[] = "master-logger (>=1.1.11)"
depends[] = "master-api (>=3.4.2)"
depends[] = "master-sistema (>=1.0.16)"
depends[] = "inkscape"
depends[] = "php5-imap | php-imap"
maintainer = "Master training s.r.l. - <PERSON><PERSON><PERSON> <<EMAIL>>"
description = "Mastercom 2 - Gestionale Amministrativo - Backend (No Framework)"

src_path = /var/lib/mercurial-server/repos/mc2api
dest_path = /var/www/mc2
;conffiles[] = /etc/mastercom2/conf_milano.php
;backup[] = /etc/myconf

; The script must to be into RELEASE path
;lock_script = lock.sh [lock.php]
;unlock_script = unlock.sh [unlock.php]

;redirect_page = updating.html  <=  NON ANCORA IMPLEMENTATO
;exclude[] = /var/lib/mercurial-server/repos/mc2api/...

check_php_errors = off


[database]
configuration = /var/www/mc2/configurations/init-db.php
create_database = on

;label_host = "host"
label_port = "{{ port }}"
;label_name = "dbname"
;label_user = "user"
;label_password = "password"

name = mastercom2
schema = public
port = 5432
host = localhost
user = postgres
password = postgres

update_port = on
backup = on
reindex = off
vacuum = off
analyze = on

parameters_table = parameter
parameter_name_column = name
parameter_value_column = value
version_label = VERSION


[services]
apache_reload = on
apache_restart = off
postgresql_reload = off
postgresql_restart = off


[optimizer]
php = on
js = on
css = on
html = on


[execute]
file[] = update.sql
