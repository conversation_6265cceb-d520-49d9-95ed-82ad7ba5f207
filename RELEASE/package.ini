[application]

name = mc2-api
version = 3.25.42

section = main
priority = optional
architecture = all
depends[] = "master-mc2 (>=3.8.0)"
depends[] = "php5-curl | php-curl"
depends[] = "default-jre"
maintainer = "Master training s.r.l. - <PERSON><PERSON><PERSON> <m.cellar<PERSON><EMAIL>>"
description = "Mastercom 2 - Gestionale Amministrativo - Backend (Zend)"

src_path = /var/lib/mercurial-server/repos/mc2-api
dest_path = /var/www-source/mc2-api

exclude[] = /var/lib/mercurial-server/repos/mc2-api/vendor/smarty/smarty/demo
exclude[] = /var/lib/mercurial-server/repos/mc2-api/vendor/tecnick.com/tcpdf/examples
exclude[] = /var/lib/mercurial-server/repos/mc2-api/vendor/propel/propel1/test
exclude[] = /var/lib/mercurial-server/repos/mc2-api/composer.json
exclude[] = /var/lib/mercurial-server/repos/mc2-api/composer.lock
exclude[] = /var/lib/mercurial-server/repos/mc2-api/composer.phar
exclude[] = /var/lib/mercurial-server/repos/mc2-api/php-cs-fixer-tmp.phar
exclude[] = /var/lib/mercurial-server/repos/mc2-api/docs

conffiles[] = /etc/profile.d/mc2_magister.sh

check_php_errors = off

[database]
configuration = /var/www-source/mc2-api/data/database/build/conf/mc2-conf.php
create_database = off

label_port = 5432


name =
schema =
port =
host =
user =
password =

update_port = on
backup = off
reindex = off
vacuum = off
analyze = off

parameters_table =
parameter_name_column =
parameter_value_column =
version_label =


[services]
apache_reload = on
apache_restart = off
postgresql_reload = off
postgresql_restart = off


[optimizer]
php = off
js = on
css = on
html = on


[execute]
file[] = update.sql
