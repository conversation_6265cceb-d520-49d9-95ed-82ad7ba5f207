/*
 * File: app.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

// @require @packageOverrides
Ext.Loader.setConfig({
    enabled: true
});


Ext.application({
    models: [
        'ProtocolProtocol',
        'SettingsGroup',
        'Permission',
        'SettingsUser',
        'Employee',
        'CoreCity',
        'CoreCountry',
        'SocialPosition',
        'Absence',
        'AbsenceKind',
        'DayTimeTable',
        'TimeTable',
        'WeekBorder',
        'Presence',
        'BudgetActivities',
        'AbsenceStack',
        'ExtraordinaryStored',
        'ExtraordinaryAbsStack',
        'HomeInfo',
        'Institute',
        'HomeReleaseNote',
        'EventError',
        'UserLog',
        'Decreto',
        'CcpMovement',
        'CcpType',
        'Classe',
        'CcpBollettino',
        'CorePrintSpool',
        'CoreBankAccount',
        'CcpPaymentMethod',
        'EmployeeParameters',
        'EmployeeTree',
        'CcpSubject',
        'PersonnelHourType',
        'PersonnelProject',
        'CcpStudent',
        'CcpSchoolYear',
        'MC2Table',
        'DataLookupResult',
        'MC2Parameter',
        'InstituteType',
        'StackPersonnelLink',
        'ArchiveDocument',
        'ProtocolSubjectKind',
        'ProtocolSendMethod',
        'ProtocolType',
        'ProtocolAction',
        'ArchiveMetadata',
        'ArchiveClass',
        'CoreParameter',
        'ProtocolCorrespondent',
        'TrasparenzaVoice',
        'AlboPublication',
        'AlboEntity',
        'AlboCategory',
        'ProtocolHistory',
        'ArchiveOrigin',
        'ProtocolCorrespondentOrigin',
        'AlboArea',
        'AlboHistory',
        'CcpCategory',
        'CcpAdditional',
        'CcpAdditionalLinked',
        'CcpResidual',
        'CcpPayment',
        'CcpPayer',
        'CcpReceipt',
        'PersonnelHourTypeLinked',
        'PersonnelProjectLinked',
        'Register',
        'ArchiveMailAccount',
        'ArchiveMail',
        'DocumentFlow',
        'DocumentStep',
        'ArchiveClassStep',
        'ArchiveMailAttachment',
        'ArchiveDocumentFile',
        'RemoteClass',
        'MyModel',
        'Assignee',
        'ArchiveOffice',
        'ArchiveTemplate',
        'ArchiveDossier',
        'Dossier',
        'Raccolte',
        'Contact',
        'ContactGroup',
        'CcpTypeStep',
        'GenericSearch',
        'CcpInvoice',
        'CcpInvoiceTransmission',
        'CcpDepositSlip',
        'Credit',
        'CcpStudentMarket',
        'CcpParent',
        'CcpReminder',
        'CcpServizi',
        'CcpDeposit',
        'CreditType',
        'Discount',
        'CcpVatCode',
        'Invoice',
        'Supplier',
        'Indirizzo',
        'StudentBalance',
        'MagisterEsercizio',
        'Corrispettivo',
        'CcpReport',
        'CcpInvoiceDepositSlip'
    ],
    stores: [
        'ProtocolProtocols',
        'SettingsGroups',
        'SettingsUsers',
        'Employees',
        'CoreCities',
        'CoreCountries',
        'Gender',
        'SocialPosition',
        'Absences',
        'AbsenceKindsLinked',
        'CalendarTimeTables',
        'Months',
        'Years',
        'TimeTables',
        'WeekBorders',
        'Presences',
        'InOutType',
        'InOut',
        'BudgetActivities',
        'CalendarHoliday',
        'AbsenceStacks',
        'AbsenceStackUnits',
        'EmpUnitRecoverHours',
        'EmployeesAll',
        'ExtraordinaryStored',
        'ExtraordinaryAbsStack',
        'MastertrainingContacts',
        'HomeInfos',
        'Institutes',
        'HomeReleaseNotes',
        'EventErrors',
        'UserLogs',
        'Decreti',
        'AbsenceKinds',
        'CcpTypes',
        'McDbs',
        'CcpMovements',
        'Classi',
        'Students',
        'BolTaxesType',
        'CorePrintSpool',
        'CoreBankAccounts',
        'CcpPaymentMethods',
        'CcpPaymentDestinations',
        'EmployeeParameters',
        'EmployeesTree',
        'CcpSubjects',
        'AbsenceStacksAndExtraordinary',
        'EmployeesTreeActive',
        'PersonnelProjects',
        'CcpPaymentMethodsFilter',
        'CcpPaymentDestinationsFilter',
        'CcpTypesFilter',
        'CcpBollettini',
        'Permissions',
        'CcpStudentsTree',
        'CcpSchoolYearsFilter',
        'CcpTypeSchoolYears',
        'MC2Tables',
        'DataLookupResults',
        'MC2Parameters',
        'SchoolTypes',
        'StackResetTypes',
        'StacksPersonnelLinks',
        'ArchiveDocumentsArchived',
        'ArchiveClasses',
        'ProtocolSubjectKinds',
        'ProtocolSendMethods',
        'ProtocolTypesTree',
        'ProtocolActions',
        'ProtocolTypes',
        'ArchiveMetadata',
        'CoreParameter',
        'ArchiveClassMetadata',
        'ArchiveClassesFilter',
        'ArchiveServices',
        'ProtocolTypesLeaf',
        'ProtocolCorrespondents',
        'ProtocolCorrespondentsFilter',
        'ProtocolSendMethodsFilter',
        'ProtocolSubjectKindsFilter',
        'ProtocolTypesFilter',
        'AlboPublications',
        'AlboCategories',
        'AlboCategoriesFilter',
        'AlboEntities',
        'AlboEntitiesFilter',
        'ProtocolDirectionsFilter',
        'ProtocolLinkedDocuments',
        'ProtocolLinkedCorrespondents',
        'ProtocolLinkedProtocols',
        'ProtocolLinkedDocumentsForm',
        'ProtocolLinkedProtocolsForm',
        'ProtocolProtocolsForm',
        'ProtocolDocumentsForm',
        'ProtocolHistories',
        'ArchiveDocumentsUser',
        'ArchiveOrigins',
        'ArchiveOriginsFilter',
        'ProtocolCorrespondentsForm',
        'ProtocolLinkedCorrespondentsForm',
        'ProtocolCorrespondentsOrigins',
        'ProtocolCorrespondentsOriginsForm',
        'TrasparenzaVoicesTree',
        'TrasparenzaLinkedDocuments',
        'TrasparenzaLinkedDocumentsForm',
        'TrasparenzaDocumentsForm',
        'TrasparenzaVoicesPickerTree',
        'AlboCategoryDurations',
        'AlboAreas',
        'AlboAreasFilter',
        'AlboExpirationsFilter',
        'AlboStatusesFilter',
        'AlboPublishedFilter',
        'AlboDocumentsForm',
        'AlboLinkedDocuments',
        'AlboLinkedDocumentsForm',
        'AlboHistories',
        'ArchiveActionProtocolFilter',
        'ArchiveActionAlboFilter',
        'ArchiveActionTrasparenzaFilter',
        'ProtocolBarcodes',
        'CcpCategories',
        'CcpCategoriesFilter',
        'CcpAdditionals',
        'CcpLinkedAdditionalsForm',
        'CcpLinkedAdditionals',
        'CcpLinkedPayments',
        'CcpDirectionsFilter',
        'CcpAdditionalsForm',
        'CcpPayers',
        'CcpPayerTypes',
        'CcpResidualsGroupings',
        'CcpPaymentsUnreceipted',
        'CcpReceipts',
        'CoreBankAccountTypes',
        'CcpPayments',
        'CcpResidualsYears',
        'CcpResidualsTypes',
        'CcpResiduals',
        'CcpPrintTypes',
        'CcpPrintGroupings',
        'PersonnelPersonnelProjects',
        'PersonnelProjectHourTypes',
        'PersonnelHourTypes',
        'CcpSubjectTypes',
        'CcpSubjectTypesFilter',
        'CcpPayerTypesFilter',
        'Register',
        'ArchiveMailAccounts',
        'ArchiveMails',
        'ArchiveSentMails',
        'DocumentFlows',
        'ArchiveClassSteps',
        'ArchiveMailAttachments',
        'ArchiveDocumentFiles',
        'ArchiveMailSecurity',
        'ArchiveMailProtocols',
        'PrintFormats',
        'RemoteClass',
        'ArchiveDocumentMails',
        'AbsenceKindGrouped',
        'ArchiveSignTypes',
        'ArchiveMetadataFiles',
        'MyModels',
        'Assignees',
        'ArchiveMailsUser',
        'ArchiveStores',
        'ArchiveOffices',
        'ArchiveOfficeUsers',
        'ArchiveDocumentsOffice',
        'ArchiveMailsOffice',
        'ArchiveDashboard',
        'ArchiveCompletedFilter',
        'ArchiveCheckedFilter',
        'ArchiveTemplates',
        'ArchiveDossiers',
        'ArchiveDocumentDossier',
        'Raccoltes',
        'AddressBook',
        'Contacts',
        'ArchiveMailSendContact',
        'ContactGroups',
        'ContactGroupLinked',
        'ProtocolSignPositions',
        'ArchiveDocumentDossierLinked',
        'ArchiveDocumentDossierMc',
        'CcpTypeSteps',
        'CcpDateTypeFilter',
        'GenericSearches',
        'CcpInvoices',
        'CcpInvoiceMovements',
        'CcpInvoiceTransmissions',
        'CcpDepositSlips',
        'CcpInvoicesToDepositSlip',
        'CcpInvoiceAccountHolders',
        'OLDCcpDepositSlipRows',
        'CcpStudentMovements',
        'Credits',
        'CcpStudentMarkets',
        'CcpStudentMovementsMensa',
        'CcpParents',
        'CcpReminders',
        'StudentStates',
        'CcpReminderDetails',
        'CcpPastiConsumati',
        'CcpPastiRim',
        'CcpStudents',
        'CcpServizi',
        'CcpServiceGrouping',
        'CcpDeposits',
        'CcpPrintWhat',
        'CcpPrintCategories',
        'CcpPrintStudentCols',
        'CreditsType',
        'CcpMovementsCover',
        'Discounts',
        'CcpStudentsDiscount',
        'CcpFamilies',
        'CcpVatCodes',
        'Invoices',
        'Suppliers',
        'InvoiceExpirations',
        'CcpConsolidations',
        'CcpStudentReceipts',
        'Indirizzi',
        'StudentBalances',
        'MagisterEsercizi',
        'Corrispettivi',
        'CcpMcPrintTemplate',
        'CcpReports',
        'Indirizzi1',
        'Classi1',
        'CcpReportStudentsParents',
        'CcpAddMovementStudents',
        'CcpAeCategories',
        'CreditsTypePaymentFilter',
        'ListaFratelli',
        'CcpInvoiceDepositSlips',
        'CcpReceiptAddressCode',
        'CcpPrintOfCategories',
        'CcpPrintCategoryMovementTypes',
        'CcpStudentsPayments',
        'CcpTypesFilter1',
        'CcpCategoriesFilter1',
        'ReminderTypes',
        'ProtocolMechanCodes'
    ],
    views: [
        'LoginView',
        'GroupEditWin',
        'UserEditWin',
        'PaswdEditWin',
        'AbsenceNewWin',
        'PresencesNewWin',
        'EmployeeProjectsEditWin',
        'EmployeeHolidayCalendarWin',
        'EmployeeAbsenceStackWin',
        'EmployeePresencesPrintWin',
        'DispatcherView',
        'InstituteEditWin',
        'CcpMovementEditWin',
        'MainView',
        'TimeTableDeleteWin',
        'TimeTableEditWin',
        'TimeTableCopyWeekWin',
        'AbsenceDecretoWin',
        'CcpTypeEditWin',
        'EmployeeResidualsPrint_Win',
        'EmployeeEntriesExitsPrint_Win',
        'EmployeeAbsencesPrintWin',
        'EmployeeLastLockedMonthPrintWin',
        'EmployeeTimetablePrintWin',
        'EmployeeDayPrintWin',
        'EmployeeParametersCopyWin',
        'EmployeeAbsenceCopyWin',
        'EmployeeProjectsHourTypesWin',
        'EmployeeAbsenceStackEditWin',
        'CcpTypesWin',
        'CcpBollettiniPrintWin',
        'ProtocolPnl',
        'CcpCashJournalPrintWin',
        'AdminDataLookupWin',
        'AdminParametersWin',
        'InstitutesWin',
        'EmployeeStacksCopyWin',
        'ArchivePnl',
        'EmployeePnl',
        'CcpPnl',
        'SettingsPanel',
        'MailAccountPnl',
        'ProtocolTypesWin',
        'ProtocolSendMethodEditWin',
        'ProtocolSubjectKindEditWin',
        'ArchiveManagementWin',
        'ArchiveDocumentUploadWin',
        'ProtocolTypeEditWin',
        'ProtocolCorrespondentsWin',
        'ProtocolCorrespondentEditWin',
        'ProtocolProtocolNewWin',
        'AlboPnl',
        'TrasparenzaPnl',
        'AlboCategoriesWin',
        'TrasparenzaVoiceEditWin',
        'ProtocolLinkedDocumentsWin',
        'ProtocolLinkedCorrespondentsWin',
        'ProtocolLinkedProtocolsWin',
        'ProtocolProtocolNewLinkedDocumentsPickerWin',
        'ProtocolProtocolNewLinkedProtocolsPickerWin',
        'ProtocolActionsWin',
        'ProtocolSubjectKindsWin',
        'ProtocolSendMethodsWin',
        'ArchiveDocumentEditWin',
        'ArchiveDocumentArchiveWin',
        'ProtocolProtocolNewLinkedCorrespondentsPickerWin',
        'ProtocolCorrespondentEditOriginPickerWin',
        'TrasparenzaLinkedDocumentsPickerWin',
        'TrasparenzaVoicePickerWin',
        'TrasparenzaLinkedDocumentsWin',
        'AlboAreasWin',
        'AlboAreaEditWin',
        'AlboEntityEditWin',
        'AlboCategoryEditWin',
        'AlboPublicationLinkedDocumentsPickerWin',
        'AlboPublicationLinkedDocumentsWin',
        'AlboPublicationEditWin',
        'AlboPublicationExtensionWin',
        'AlboPublicationHistoryWin',
        'CcpByCCPrintWin',
        'ProtocolPrintsBarcodeWin',
        'LoginPasswordExpired',
        'CcpCategoriesWin',
        'CcpCategoryEditWin',
        'CcpAdditionalsWin',
        'CcpAdditionalEditWin',
        'CcpLinkedAdditionalsWin',
        'CcpLinkedAdditionalsPickerWin',
        'CcpPaymentEditWin',
        'CcpLinkedPaymentsWin',
        'CcpResidualsWin',
        'CcpReceiptEditWin',
        'CcpReceiptEditMultiWin',
        'CcpMovementCopyWin',
        'CcpPrintTypeWin',
        'EmployeeProjectsHourTypeEditWin',
        'EmployeeProjectsProjectEditWin',
        'RegisterWin',
        'RegisterAddWin',
        'RegisterDetailWin',
        'ArchiveMailViewWin',
        'ArchiveClassWin',
        'ArchiveSignWin',
        'ArchiveMailSendWin',
        'ArchiveDocumentMailWin',
        'ArchiveSignRemoteWin',
        'ArchiveSignRemoteLogin',
        'ArchiveSignRemoteOtpWin',
        'ArchiveMetadataFileWin',
        'ArchiveDocumentCheckWin',
        'ArchiveDocumentViewWin',
        'ArchiveDashboardWin',
        'ArchiveTemplateWin',
        'ArchiveDossierWin',
        'ArchiveMassiveSignWin',
        'RaccoltaAssignWin',
        'ContactAddWin',
        'MailingListWin',
        'ArchiveDossierNewWin',
        'CcpInvoiceNewWin',
        'CcpSignLoginWin',
        'CcpInvoiceTransmissionWin',
        'CcpInvoiceAccountHolderWin',
        'CcpDepositSlipNewWin',
        'CcpInvoiceEditWin',
        'CcpDepositSlipRowWin',
        'ExportEasyWin',
        'EasyImportWin',
        'CcpStudentsMissing',
        'ReminderDetailWin',
        'CcpServiziWin',
        'ImportPaymentsEasyWin',
        'CcpPvrUploadWin',
        'CcpDepositWin',
        'CcpCreditUploadWin',
        'CcpCreditDistributionWin',
        'CcpReminderCustomWin',
        'CcpAdditionalTemplateWin',
        'CcpDiscountWin',
        'InvoiceEditWin',
        'MpExtraTimeEditWin',
        'CcpExtraTimeWin',
        'CcpAttestazionePrintWin',
        'CcpDichiarazionePrintWin',
        'CcpCorrispettiviPrintWin',
        'CcpExcelMovementExportWin',
        'CcpAeExportWin',
        'CcpCreditTypeWin',
        'CcpAdePrintFromFileWin',
        'CcpPaymentMassiveEditWin',
        'UploadDocumentReportWin',
        'CcpExportResidualsWin',
        'CcpPrintCategoriesWin',
        'CcpPrintCategoryMovementTypeWin',
        'MyPanel9',
        'CcpYearsToSaveWin',
        'MyPanel3',
        'MyPanel29',
        'MyPanel27'
    ],
    controllers: [
        'PermissionController'
    ],
    name: 'mc2ui',

    launch: function() {
        Ext.create('mc2ui.view.DispatcherView');
        // Globals
        mc2ui.app.permissionUI = {};
        mc2ui.app.settings = {};
        mc2ui.app.ccpMovementsFilterEventTimeoutId = 0;
        mc2ui.app.ccpPaymentsFilterEventTimeoutId = 0;
        mc2ui.app.ccpReceiptsFilterEventTimeoutId = 0;
        mc2ui.app.warehouseFilterEventTimeoutId = 0;
        mc2ui.app.inventoryFilterEventTimeoutId = 0;
        mc2ui.app.archiveFilterEventTimeoutId = 0;
        mc2ui.app.protocolFilterEventTimeoutId = 0;
        mc2ui.app.alboFilterEventTimeoutId = 0;
        mc2ui.app.printSpool = false;


        // Locale number format
        Ext.util.Format.thousandSeparator = '.';
        Ext.util.Format.decimalSeparator = ',';

        // Add the additional 'zipcode' VTypes
        // 5 chars long, only numeric
        Ext.apply(Ext.form.field.VTypes, {
            zipcode: function(val, field) {
                var regex = /\d{5}/;

                if (parseInt(val) < 10 || parseInt(val) > 99000 || val.length != 5 || !regex.test(val)) {
                    return false;
                }
                return true;
            },

            zipcodeText: 'Il CAP deve avere 5 caratteri numerici ed essere compreso tra 00010 e 99000!'
        });

        // Add the additional 'daterange' VTypes
        // For each From-To datefield couple insert startDateField and endDateField field setting the value to the id of the corresponding opposite field.
        Ext.apply(Ext.form.field.VTypes, {
            daterange: function(val, field) {
                var date = field.parseDate(val);

                if (!date) {
                    return false;
                }

                if (field.startDateField && (!this.dateRangeMax || (date.getTime() != this.dateRangeMax.getTime()))) {
                    var start = field.up('form').down('#' + field.startDateField);
                    start.setMaxValue(date);
                    start.validate();
                    this.dateRangeMax = date;
                } else if (field.endDateField && (!this.dateRangeMin || (date.getTime() != this.dateRangeMin.getTime()))) {
                    var end = field.up('form').down('#' + field.endDateField);
                    end.setMinValue(date);
                    end.validate();
                    this.dateRangeMin = date;
                }
                return true;
            },

            daterangeText: 'La data di fine deve essere successiva alla data di inizio!'
        });

        // Add the additional 'timerange' VTypes
        // For each From-To timefield couple insert startTimeField and endTimeField field setting the value to the id of the corresponding opposite field.
        Ext.apply(Ext.form.field.VTypes, {
            timerange: function(val, field) {
                var t = val.split(':'),
                    time = field.parseDate(new Date(1970, 1, 1, t[0], t[1], 0, 0));

                if (!time) {
                    return false;
                }

                if (field.startTimeField && (!this.timeRangeMax || (time.getTime() != this.timeRangeMax.getTime()))) {
                    var start = field.up('form').down('#' + field.startTimeField);
                    start.setMaxValue(time);
                    this.timeRangeMax = time;
                    start.validate();
                } else if (field.endTimeField && (!this.timeRangeMin || (time.getTime() != this.timeRangeMin.getTime()))) {
                    var end = field.up('form').down('#' + field.endTimeField);
                    end.setMinValue(time);
                    this.timeRangeMin = time;
                    end.validate();
                }
                return true;
            },

            timerangeText: 'L\'orario di fine deve essere successivo all\'orario di inizio!'
        });


        Ext.Ajax.request({
            url: '/mc2/applications/core/checkUserSession.php',
            success: function(response) {
                res = Ext.decode(response.responseText);
                mc2ui.app.setGlobalProfile(res);
                if (res.success === true) {
                    Ext.getStore('HomeInfos').load();
                    Ext.Ajax.request({
                        url: '/mc2/applications/core/permissionUI.php',
                        success: function(response){
                            permissions = Ext.decode(response.responseText);
                            mc2ui.app.permissionUI = permissions.results;
                            if (Ext.getCmp('LoginView') !== undefined) {
                                Ext.getCmp('LoginView').destroy();
                            }

                            Ext.widget('MainView').show();

                            // MT.Logger.UserId = res.uid;
                            // MT.Logger.UserName = res.username;

                            mc2ui.app.initMc2();
                        }
                    });
                } else {
                    Ext.widget('LoginView').show();
                }
            }
        });


        var body = Ext.getBody();
        body.on('contextmenu',function(e){
            e.stopEvent();
        });


        Ext.Ajax.request({
            url: '/mc2/applications/core/other/years.php?school_year_only=true',
            success: function(response){
                res = Ext.decode(response.responseText);
                if (res.success === true) {
                    mc2ui.app.settings['mcDb'] = {
                        schoolYear: res.results,
                        name: 'mastercom_' + res.results.replace('/','_')
                    };
                }
            }
        });


        // Logger section
        // MT.Logger.AppName = 'mc2ui'; // TO DO IN RUNTIME
        // MT.Logger.AppVersion = '-1'; // TO DO IN RUNTIME


        Ext.Ajax.on('requestcomplete', function(a, res){
            mc2ui.app.errorCodeHandler(Ext.decode(res.responseText));
        }, this);
    },

    showNotifyPrint: function(results) {
        if (results.spool === true) {
            Ext.create('widget.uxNotification', {
                title: 'Stampa',
                position: 'tr',
                manager: 'instructions',
                cls: 'ux-notification-light',
                iconCls: 'ux-notification-icon-information',
                html: 'La stampa è in elaborazione. <br /> Controllare lo stato nel pannello "Generale".',
                autoCloseDelay: 6000,
                slideInDuration: 500,
                slideInAnimation: 'easeIn'
            }).show();
        } else {
            window.open(results.urlPath);
        }

    },

    showNotifySave: function() {
        Ext.create('widget.uxNotification', {
            title: 'Salvataggio',
            position: 'tr',
            manager: 'instructions',
            cls: 'ux-notification-light',
            iconCls: 'ux-notification-icon-information',
            html: 'I dati sono stati salvati',
            autoCloseDelay: 3000,
            slideInDuration: 500,
            slideInAnimation: 'easeIn'
        }).show();
    },

    openPrint: function(id) {
        var store = Ext.getStore('CorePrintSpool'),
            record = store.getById(id);

        window.open('/mc2-api/core/print/' + record.get('id'));

        setTimeout(function() {
            store.remove(record);
            store.sync();
            store.load();
        }, 500);
    },

    initMc2: function() {
        // Loads static stores
        this.loadStaticStores();


        // Initializes print spool "daemon"
        this.spoolInterval = setInterval(function() {
            if (mc2ui.app.printSpool) {
                return;
            }

            mc2ui.app.printSpool = true;

            Ext.getStore('CorePrintSpool').load({
                callback: function(res) {
                    mc2ui.app.printSpool = false;

                    Ext.each(res, function(r) {
                        if (r.get('notified') === false && r.get('completed') === true) {
                            Ext.create('widget.uxNotification', {
                                title: 'Stampa pronta',
                                position: 'tr',
                                manager: 'instructions',
                                cls: 'ux-notification-light',
                                iconCls: 'ux-notification-icon-information',
                                html: '<a href="#" onClick="mc2ui.app.openPrint(' + r.get('id') + ');"> ' + r.get('name') + '</a> ',
                                autoCloseDelay: 10000,
                                slideInDuration: 500,
                                slideInAnimation: 'easeIn',
                                stickOnClick: false
                            }).show();
                        }
                    });
                }
            });
        }, 5000);
    },

    setGlobalProfile: function(res) {

        mc2ui.app.settings.username = res.username;
        mc2ui.app.settings.uid = res.uid;
        mc2ui.app.settings.offices = res.offices;
        mc2ui.app.settings.year = res.year;
        mc2ui.app.settings.canModifyProtocol = res.modify;
        mc2ui.app.settings.signType = res.sign_type;
        mc2ui.app.settings.prints = res.prints;
        mc2ui.app.settings.invoiceEnabled = res.invoiceEnabled;
        mc2ui.app.settings.sepaUpdateInfo = res.sepa_update_info;
        mc2ui.app.settings.sepaCheckBrother = res.sepa_check_brother;
        mc2ui.app.settings.magisterEnabled = res.magisterEnabled;
        mc2ui.app.settings.creditNotesPublish = res.credit_notes_publish;
        mc2ui.app.settings.familyManagementEnabled = res.familyManagementEnabled;
        mc2ui.app.settings.ccp_filter_movement_by_school_year = res.ccp_filter_movement_by_school_year;
        mc2ui.app.settings.easy_mc2_api = res.easy_mc2_api;
        mc2ui.app.settings.oldCreditNoteMethod = res.oldCreditNoteMethod;
        mc2ui.app.settings.easyContiRicaviRisconti = res.easy_conti_ricavi_risconti;
        mc2ui.app.settings.areaArchive = res.area_archive;
        mc2ui.app.settings.areaPersonnel = res.area_personnel;
        mc2ui.app.settings.areaCcp = res.area_ccp;
        mc2ui.app.settings.areaWarehouse = res.area_warehouse;
        mc2ui.app.settings.areaProtocol = res.area_protocol;
        mc2ui.app.settings.areaAlbo = res.area_albo;
        mc2ui.app.settings.areaTrasparenza = res.area_trasparenza;
        mc2ui.app.settings.areaSettings = res.area_settings;
        mc2ui.app.settings.areaMailAccount = res.area_mail_account;
        mc2ui.app.settings.couchTk = res.couch_tk;

        if(res.permissions) {
            mc2ui.app.settings.canManageReservedProtocol = res.permissions.PROTOCOLLO_RISERVATO;
            mc2ui.app.settings.discountEnabled = res.permissions.DISCOUNT_ENABLED;
        }

        if (res.def) mc2ui.app.settings.def_paymenth_method = parseInt(res.def.paymenth_method);


    },

    treePropagateChange: function(node) {

        // Propagate change downwards
        var setChildrenCheckedStatus = function (current) {
            if (current.parentNode) {
                var parent = current.parentNode;
                current.set('checked', parent.get('checked'));
            }

            if (current.hasChildNodes()) {
                current.eachChild(arguments.callee);
            }
        };

        // Propagate change upwards
        var updateParentCheckedStatus = function (current) {
            if (current.parentNode) {
                var parent = current.parentNode;

                var checkedCount = 0;
                parent.eachChild(function(n) {
                    checkedCount += (n.get('checked') ? 1 : 0);
                });

                // Children have same value if all of them are checked or none is checked.
                var sameValue = (checkedCount == parent.childNodes.length) || (checkedCount == 0);

                if (sameValue) {
                    var checkedValue = (checkedCount == parent.childNodes.length);
                    parent.set('checked', checkedValue);
                } else {
                    parent.set('checked', false);
                }

                updateParentCheckedStatus(parent);
            }
        };

        // Execute propagation
        if (node.hasChildNodes()) {
            node.eachChild(setChildrenCheckedStatus);
        }
        updateParentCheckedStatus(node);



    },

    errorCodeHandler: function(response) {
        if (response.success === false) {
            switch (response.status) {
                case 401:
                    window.location.href = '/mc2ui/';
                    break;
            }
        }
    },

    loadStaticStores: function() {
        // Sets usable School Years for the taxes
        var year = new Date().getFullYear(),
            month = new Date().getMonth(),
            store = Ext.getStore('CcpTypeSchoolYears');

        store.removeAll();

        //store.add({value: 'TUTTI'});

        if (month > 7) {
            store.add({value: (year - 6) + '/' + (year - 5)});
            store.add({value: (year - 5) + '/' + (year - 4)});
            store.add({value: (year - 4) + '/' + (year - 3)});
            store.add({value: (year - 3) + '/' + (year - 2)});
            store.add({value: (year - 2) + '/' + (year - 1)});
            store.add({value: (year - 1) + '/' + year});
            store.add({value: year + '/' + (year + 1)});
            store.add({value: (year + 1) + '/' + (year + 2)});
            store.add({value: (year + 2) + '/' + (year + 3)});
            store.add({value: (year + 3) + '/' + (year + 4)});
        } else {
            store.add({value: (year - 6) + '/' + (year - 5)});
            store.add({value: (year - 5) + '/' + (year - 4)});
            store.add({value: (year - 4) + '/' + (year - 3)});
            store.add({value: (year - 3) + '/' + (year - 2)});
            store.add({value: (year - 2) + '/' + (year - 1)});
            store.add({value: (year - 1) + '/' + year});
            store.add({value: year + '/' + (year + 1)});
            store.add({value: (year+1) + '/' + (year + 2)});
            store.add({value: (year+2) + '/' + (year + 3)});
        }
    },

    linkify: function(inputText) {
        var inputText = inputText ? inputText : '',
            replacePattern1 = /(\b(https?|ftp):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/gim,
            replacedText    = inputText.replace(replacePattern1, '<a href="$1" target="_blank">$1</a>'),
            //URLs starting with www. (without // before it, or it'd re-link the ones done above)
            replacePattern2 = /(^|[^\/])(www\.[\S]+(\b|$))/gim,
            //Change email addresses to mailto:: links
            replacePattern3 = /(\w+@[a-zA-Z_]+?\.[a-zA-Z]{2,6})/gim;

        replacedText = replacedText.replace( replacePattern2, '$1<a href="http://$2" target="_blank">$2</a>' );
        replacedText = replacedText.replace( replacePattern3, '<a href="mailto:$1">$1</a>' );

        return replacedText;

    },

    convertTimestamp: function(datetime) {
        var currentTime = new Date(),
            month = currentTime.getMonth() + 1,
            day = currentTime.getDate(),
            year = currentTime.getFullYear(),
            today = day +'/'+ month +'/'+ year,
            date = Ext.Date.format(datetime, 'j/n/Y');

        if( today == date ) {
            date = Ext.Date.format( datetime, 'G:i');
        } else {
            date = Ext.Date.format( datetime, 'd/m/Y');
        }

        return date;
    }

});

